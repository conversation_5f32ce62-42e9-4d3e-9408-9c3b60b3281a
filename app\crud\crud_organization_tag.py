from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.organization_tag import OrganizationTagCreate, OrganizationTagUpdate
from app.schemas.enums import TagType


class CRUDOrganizationTag(CRUDBase[models.OrganizationTag, OrganizationTagCreate, OrganizationTagUpdate]):
    
    def get_by_org_and_type(
        self, 
        db: Session, 
        organization_id: str, 
        type_of_tags: TagType
    ) -> Optional[models.OrganizationTag]:
        """Get tags for a specific organization and tag type"""
        return db.query(self.model).filter(
            self.model.organization_id == organization_id,
            self.model.type_of_tags == type_of_tags.value
        ).first()
    
    def get_all_by_org(
        self, 
        db: Session, 
        organization_id: str
    ) -> List[models.OrganizationTag]:
        """Get all tags for a specific organization"""
        return db.query(self.model).filter(
            self.model.organization_id == organization_id
        ).all()
    
    def create_or_update(
        self,
        db: Session,
        organization_id: str,
        type_of_tags: TagType,
        tags: List[str]
    ) -> models.OrganizationTag:
        """Create new tags or update existing ones for an organization and type"""
        existing = self.get_by_org_and_type(db, organization_id, type_of_tags)
        
        if existing:
            # Update existing record
            existing.tags = tags
            db.add(existing)
            db.commit()
            db.refresh(existing)
            return existing
        else:
            # Create new record
            obj_in = OrganizationTagCreate(
                type_of_tags=type_of_tags,
                tags=tags
            )
            new_tag = self.model(
                organization_id=organization_id,
                type_of_tags=type_of_tags.value,
                tags=tags
            )
            db.add(new_tag)
            db.commit()
            db.refresh(new_tag)
            return new_tag


organization_tag = CRUDOrganizationTag(models.OrganizationTag)
