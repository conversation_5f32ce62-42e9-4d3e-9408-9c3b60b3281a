from fastapi import Depends, APIRouter


from app.api.endpoints import (
    admin,
    contacts,
    opportunity_finder,
    quality,
    reports,
    proposals,
    player_records,
    team_requests,
    lookup,
    matching,
    agents_map,
    staff_map,
    staff_finder,
    contracts,
    contract_uploads,
    contract_extraction,
    power_bi_embed,
    club_finder,
    notifications,
    community_proposal,
    player_suitability,
    organizations,
    football_field,
    logo_uploads,
    message_subscription,
    subscriptions,
    activity,
    comment,
    platform_notifications,
    player_upload,
    staff_upload,
    community_tokens,
    community_deal,
    staff_records,
    team_page,
    request_field,
    admin_changes,
    priority_players,
    favourite_teams,
    organization_tags,
)
from app.api.endpoints.users import users
from app.api import deps

api_router = APIRouter()
api_router.include_router(contacts.router, prefix="/contacts", tags=["contacts"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(
    organizations.router, prefix="/organizations", tags=["organizations"]
)
api_router.include_router(admin.router, prefix="/purchases", tags=["purchases"])
api_router.include_router(
    player_records.router, prefix="/player_records", tags=["player_records"]
)
api_router.include_router(
    player_upload.router, prefix="/player_upload", tags=["player_records"]
)
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(proposals.router, prefix="/proposals", tags=["proposals"])
api_router.include_router(
    team_requests.router, prefix="/team_requests", tags=["team_requests"]
)
api_router.include_router(lookup.router, prefix="/lookup", tags=["lookup"])
api_router.include_router(matching.router, prefix="/matching", tags=["matching"])
api_router.include_router(agents_map.router, prefix="/agents_map", tags=["agents"])
api_router.include_router(staff_map.router, prefix="/staff_map", tags=["staff"])
api_router.include_router(
    staff_finder.router, prefix="/staff_finder", tags=["staff_finder"]
)

api_router.include_router(
    quality.router,
    prefix="/quality",
    tags=["quality"],
    dependencies=[Depends(deps.generate_route_auth_check_func("ranks"))],
)

api_router.include_router(contracts.router, prefix="/contracts", tags=["contracts"])
api_router.include_router(
    contract_uploads.router, prefix="/contract_uploads", tags=["contracts"]
)
api_router.include_router(
    contract_extraction.router, prefix="/contract_extraction", tags=["contracts"]
)
api_router.include_router(power_bi_embed.router, prefix="/power_bi", tags=["power_bi"])
api_router.include_router(
    club_finder.router, prefix="/club_finder", tags=["club_finder"]
)
api_router.include_router(
    notifications.router, prefix="/notifications", tags=["notifications"]
)
api_router.include_router(
    community_proposal.router, prefix="/community_proposal", tags=["community_proposal"]
)
api_router.include_router(
    community_deal.router, prefix="/community_deals", tags=["activity_tracker"]
)
api_router.include_router(
    player_suitability.router, prefix="/player_suitability", tags=["player_suitability"]
)
api_router.include_router(
    football_field.router, prefix="/football_field", tags=["football_field"]
)
api_router.include_router(
    request_field.router, prefix="/request_field", tags=["request_field"]
)
api_router.include_router(
    logo_uploads.router, prefix="/logo_uploads", tags=["football_field"]
)
api_router.include_router(
    message_subscription.router,
    prefix="/message_subscription",
    tags=["message_subscription"],
)
api_router.include_router(
    subscriptions.router, prefix="/subscriptions", tags=["stripe"]
)
api_router.include_router(
    activity.router, prefix="/activity", tags=["activity_tracker"]
)
api_router.include_router(comment.router, prefix="/comment", tags=["comments"])
api_router.include_router(
    community_tokens.router, prefix="/community_tokens", tags=["community_tokens"]
)
api_router.include_router(
    platform_notifications.router,
    prefix="/platform_notifications",
    tags=["platform_notifications"],
)
api_router.include_router(
    staff_records.router, prefix="/staff_records", tags=["Staff Records"]
)
api_router.include_router(
    staff_upload.router, prefix="/staff_upload", tags=["Staff Records"]
)
api_router.include_router(team_page.router, prefix="/team_page", tags=["team_page"])
api_router.include_router(
    opportunity_finder.router, prefix="/opportunity_finder", tags=["opportunity_finder"]
)
api_router.include_router(
    admin_changes.router, prefix="/admin_changes", tags=["admin_changes"]
)
api_router.include_router(
    priority_players.router, prefix="/priority_players", tags=["priority_players"]
)
api_router.include_router(
    favourite_teams.router, prefix="/favourite_teams", tags=["favourite_teams"]
)
api_router.include_router(
    organization_tags.router, prefix="/organization_tags", tags=["organization_tags"]
)
