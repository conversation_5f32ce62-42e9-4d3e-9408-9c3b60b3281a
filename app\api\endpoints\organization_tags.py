from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.organization_tag import (
    OrganizationTagCreate, 
    OrganizationTagUpdate, 
    OrganizationTagResponse
)
from app.schemas.enums import TagType
from app import crud, models
from app.api import deps, utils

router = APIRouter()


@router.get("/", response_model=List[OrganizationTagResponse])
async def get_organization_tags(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get all tags for the current user's organization.
    Returns both player and team tags.
    """
    
    tags = crud.organization_tag.get_all_by_org(
        db=db, 
        organization_id=str(current_user.organization_id)
    )
    
    # Convert to response format
    response = []
    for tag in tags:
        response.append(OrganizationTagResponse(
            type_of_tags=TagType(tag.type_of_tags),
            tags=tag.tags
        ))
    
    # Ensure we always return both types, even if empty
    existing_types = {tag.type_of_tags for tag in tags}
    for tag_type in TagType:
        if tag_type.value not in existing_types:
            response.append(OrganizationTagResponse(
                type_of_tags=tag_type,
                tags=[]
            ))
    
    return response

@router.put("/{tag_type}", response_model=OrganizationTagResponse)
async def update_organization_tags(
    *,
    db: Session = Depends(deps.get_db),
    tag_type: TagType,
    obj_in: OrganizationTagUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update tags for a specific type (players or teams).
    This replaces all existing tags of the specified type.
    """
    # Check if user can edit tags (scout role cannot)
    if current_user.role.name == "scout":
        raise HTTPException(status_code=403, detail="Not authorized to update tags")
    
    # Update tags
    tag = crud.organization_tag.create_or_update(
        db=db,
        organization_id=str(current_user.organization_id),
        type_of_tags=tag_type,
        tags=obj_in.tags
    )
    
    return OrganizationTagResponse(
        type_of_tags=TagType(tag.type_of_tags),
        tags=tag.tags
    )
