from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.contact import (
    Contact,
    ContactUpdate,
    ContactCreate,
    WhatsAppContactCheckResponse,
)
from app import crud, models
from app.api import deps, utils
from app.utils.whatsapp_bot_security import (
    validate_whatsapp_bot_request,
    check_rate_limit,
    verify_api_key,
)

try:
    from ...config import settings
except:
    from app.config import settings

router = APIRouter()


@router.get("/", response_model=List[Contact])
def read_contacts(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve contacts.
    """
    utils.check_get_all(Contact, current_user)
    # print(crud.contact.get_all_w_org(
    #     db, current_user.organization_id, utils.can_access_sensitive(current_user)
    # ))
    return crud.contact.get_all_w_org(
        db, current_user.organization_id, utils.can_access_sensitive(current_user)
    )


@router.post("/", response_model=Contact)
def create_contact(
    *,
    db: Session = Depends(deps.get_db),
    contact_in: ContactCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new contact.
    """
    utils.check_create(Contact, current_user)
    contact = crud.contact.create_with_user(
        db=db,
        obj_in=contact_in,
        user=current_user,
    )
    return contact


@router.put("/{id}", response_model=Contact)
def update_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    contact_in: ContactUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an contact.
    """
    contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_modify(contact, current_user)
    contact = crud.contact.update(db=db, db_obj=contact, obj_in=contact_in)
    return contact


@router.get("/{id}", response_model=Contact)
def read_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get contact by ID.
    """
    contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    utils.check_get_one(contact, current_user)
    return contact


@router.get("/whatsapp/check_contact", response_model=WhatsAppContactCheckResponse)
async def check_whatsapp_contact(
    phone_number: str,
    contact_name: str,
    db: Session = Depends(deps.get_db),
    api_key_valid: bool = Depends(verify_api_key),
):
    """
    WhatsApp endpoint to find the most similar internal contact from the user's organization.
    Authenticates user by phone_number and searches for contact by name similarity.
    Returns the contact with highest similarity score from internal contacts only.
    """
    # Rate limiting check
    check_rate_limit(phone_number)

    # Validate WhatsApp bot request and get user
    current_user = await validate_whatsapp_bot_request(phone_number, db)

    # Query to find most similar internal contact using unaccent and levenshtein
    from sqlalchemy import text

    query = text(
        """
        SELECT
            c.id,
            c.email,
            c.first_name || ' ' || c.last_name AS "fullName",
            (1.0 - (levenshtein(
                lower(unaccent(c.first_name || ' ' || c.last_name)),
                lower(unaccent(:contact_name))
            )::float / GREATEST(
                length(c.first_name || ' ' || c.last_name),
                length(:contact_name),
                1
            ))) AS similarity_score
        FROM {schema}.contacts c
        WHERE c.organization_id = :org_id
        AND c.contact_type = 'internal'
        ORDER BY
            similarity_score DESC
        LIMIT 6
    """.format(
            schema=settings.PG_SCHEMA
        )
    )

    results = (
        db.execute(
            query,
            {"contact_name": contact_name, "org_id": current_user.organization_id},
        )
        .mappings()
        .all()
    )

    if not results:
        raise HTTPException(
            status_code=404, detail="No internal contacts found in organization"
        )

    # First result is the best match
    best_match = results[0]

    # Create top matches list (excluding the best match)
    top_matches = []
    for result in results[1:]:
        top_matches.append(
            {
                "contact_id": str(result["id"]),
                "email": result["email"] or "",
                "fullName": result["fullName"],
                "similarity_score": float(result["similarity_score"]),
            }
        )

    return {
        "contact_id": str(best_match["id"]),
        "email": best_match["email"] or "",
        "fullName": best_match["fullName"],
        "similarity_score": float(best_match["similarity_score"]),
        "top_matches": top_matches,
    }


@router.delete("/bulk-delete", response_model=List[Contact])
async def delete_multiple_contacts(
    *,
    db: Session = Depends(deps.get_db),
    ids: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete multiple Contacts.
    """
    if ids is None:
        raise HTTPException(status_code=400, detail="No ids provided")

    ids_list = ids.split(",")
    contacts_out = []
    for id in ids_list:
        contact = crud.contact.get_by_org(
            db=db, id=id, org_id=current_user.organization_id
        )
        utils.check_delete(contact, current_user)
        contact_out = Contact.from_orm(contact)
        contacts_out.append(contact_out)
        crud.contact.remove(db=db, id=id)
    return contacts_out


@router.delete("/{id}", response_model=Contact)
def delete_contact(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an contact.
    """
    contact = crud.contact.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_delete(contact, current_user)
    contact_out = Contact.from_orm(contact)
    crud.contact.remove(db=db, id=id)
    return contact_out
