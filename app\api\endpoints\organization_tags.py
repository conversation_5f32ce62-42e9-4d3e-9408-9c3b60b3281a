from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.organization_tag import (
    OrganizationTagCreate, 
    OrganizationTagUpdate, 
    OrganizationTagResponse
)
from app.schemas.enums import TagType
from app import crud, models
from app.api import deps, utils

router = APIRouter()


@router.get("/")
async def get_organization_tags(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get all tags for the current user's organization.
    Returns format: {players: [], teams: []}
    """
    # Check if user can access tags (any role except scout can view)
    if current_user.role.name == "scout":
        raise HTTPException(status_code=403, detail="Not authorized to view tags")

    tags = crud.organization_tag.get_all_by_org(
        db=db,
        organization_id=str(current_user.organization_id)
    )

    # Initialize response with empty arrays
    response = {
        "players": [],
        "teams": []
    }

    # Fill in the actual tags
    for tag in tags:
        if tag.type_of_tags == "players":
            response["players"] = tag.tags
        elif tag.type_of_tags == "teams":
            response["teams"] = tag.tags

    return response

@router.put("/{tag_type}", response_model=OrganizationTagResponse)
async def update_organization_tags(
    *,
    db: Session = Depends(deps.get_db),
    tag_type: TagType,
    obj_in: OrganizationTagUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update tags for a specific type (players or teams).
    This replaces all existing tags of the specified type.
    """
    # Check if user can edit tags (scout role cannot)
    if current_user.role.name == "scout":
        raise HTTPException(status_code=403, detail="Not authorized to update tags")
    
    # Update tags
    tag = crud.organization_tag.create_or_update(
        db=db,
        organization_id=str(current_user.organization_id),
        type_of_tags=tag_type,
        tags=obj_in.tags
    )
    
    return OrganizationTagResponse(
        type_of_tags=TagType(tag.type_of_tags),
        tags=tag.tags
    )
