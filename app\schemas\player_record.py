import uuid
from typing import Optional, List, TYPE_CHECKING

from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from app.schemas.enums import (
    ConstrolStage,
    Position,
    ChannelType,
    TransferStrategy,
)
from pydantic import (
    validator,
    ValidationError,
)
from pydantic import BaseModel
from app.schemas.contact import ContactShort
from app.schemas.player_info import PlayerInfo
from app.schemas.change import Change
from app.schemas.contract import ContractShort, Contract
from app.schemas.report import Report
from app.schemas.notifications import NotificationsRead
from app.schemas.player_upload import PlayerUpload
from app.schemas.assigned_to_record import AssignedToRecord
from app.schemas.comment import Comment

if TYPE_CHECKING:
    from app.schemas.report import Report


class PlayerRecordUpdate(ExtendedUpdateBase):
    assigned_to_record: Optional[List[uuid.UUID]]
    playerId: int
    control_stage: Optional[ConstrolStage]
    position: Optional[List[Position]]
    quality: Optional[int]
    potential: Optional[int]
    club_asking_price: Optional[float]
    transfer_strategy: Optional[TransferStrategy]
    description: Optional[str]
    current_gross_salary: Optional[float]
    expected_net_salary: Optional[float]
    video_link: Optional[str]

    class Config:
        use_enum_values = True
        use_cache = True
        # allow_population_by_field_name = True

        schema_extra = {
            "example": {
                "playerId": 3322,
                "firstName": "Cristinano",
                "lastName": "Ronaldo",
                "position": ["st"],
                "quality": 12,
                "potential": 12,
                "control_stage": "signed",
                "player_description": "top top top",
                "transfer_strategy": "sell",
                "notes": "very top",
                "current_gross_salary": 1200000,
            }
        }

    @validator("potential")
    def pontetial_range(cls, v):
        if v:
            if v < -1 or v > 13:
                raise ValidationError("Potential should be between -1 and 13")
            return v

    @validator("quality")
    def quality_range(cls, v):
        if v:
            if v < -1 or v > 13:
                raise ValidationError("Quality segment should be between -1 and 13")
            return v

    # @validator("id", always=True)
    # def generate_id(cls, v, values) -> int:
    #     v = values["playerId"]
    #     return v


class PlayerRecordCreateLookup(BaseModel):
    playerId: Optional[int]
    tm_player_id: Optional[int]


class PlayerRecordCreate(PlayerRecordUpdate, ExtendedCreateBase):
    playerId: int
    control_stage: Optional[ConstrolStage] = "mandate_on_demand"
    assigned_to_record: Optional[List[uuid.UUID]] = []
    position: Optional[List[Position]]
    quality: Optional[int]
    potential: Optional[int]
    club_asking_price: Optional[float]
    transfer_strategy: Optional[TransferStrategy] = "sell"
    description: Optional[str]
    current_gross_salary: Optional[float]
    expected_net_salary: Optional[float]


class PlayerRecord(PlayerRecordCreate, ExtendedBase):
    # scouting_reports: List[ReportShort]
    # deals_with_player: "List[Proposal]"
    player_info: PlayerInfo
    assigned_to_record: Optional[List[AssignedToRecord]]
    # changelog: List[Change]
    # contracts: List[Contract]
    notifications: Optional[NotificationsRead]
    suitability_score: Optional[float]
    # uploads: Optional[List[PlayerUpload]]
    # comments: List[Comment]
    is_priority: Optional[bool] = False


class WhatsAppPlayerRecordCreate(BaseModel):
    tm_player_id: int
    club_asking_price: Optional[float]
    expected_net_salary: Optional[float]
    video_link: Optional[str]
    description: Optional[str]
    control_stage: Optional[ConstrolStage] = "mandate_on_demand"


class WhatsAppPlayerRecordUpdate(PlayerRecordUpdate):
    comment: Optional[str] = None


Report.update_forward_refs(PlayerRecord=PlayerRecord)
PlayerUpload.update_forward_refs(PlayerRecord=PlayerRecord)


class PlayerRecordShort(BaseModel):
    id: uuid.UUID
    player_info: PlayerInfo

    class Config:
        orm_mode = True
        use_cache = True


class PlayerMatch(BaseModel):
    player_id: str
    playerId: int
    fullName: str
    similarity_score: float


class WhatsAppPlayerCheckResponse(BaseModel):
    player_id: str
    playerId: int
    fullName: str
    similarity_score: float
    top_matches: List[PlayerMatch]

    class Config:
        schema_extra = {
            "example": {
                "player_id": "123e4567-e89b-12d3-a456-************",
                "playerId": 3322,
                "fullName": "Cristiano Ronaldo",
                "similarity_score": 0.95,
                "top_matches": [
                    {
                        "player_id": "123e4567-e89b-12d3-a456-************",
                        "playerId": 3222,
                        "fullName": "Cristiano Manaldo",
                        "similarity_score": 0.75,
                    },
                    {
                        "player_id": "123e4567-e89b-12d3-a456-426614174200",
                        "playerId": 3122,
                        "fullName": "Christian Ronald",
                        "similarity_score": 0.65,
                    },
                ],
            }
        }


class ManuallyCreatedPlayer(BaseModel):
    first_name: str
    last_name: str
    position: Optional[List[Position]]
    date_of_birth: str
    passport: List[str]
    current_club: Optional[int]
    club_name: Optional[str]
    tm_link: Optional[str]
