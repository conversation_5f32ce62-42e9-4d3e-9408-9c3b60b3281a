from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import crud, models
from app.api import deps
from app.schemas.favourite_teams import (
    FavouriteTeamCreate,
    FavouriteTeamRead,
    FavouriteTeamDelete,
)

router = APIRouter()


@router.post("/", response_model=FavouriteTeamRead)
def create_favourite_team(
    *,
    db: Session = Depends(deps.get_db),
    favourite_team_in: FavouriteTeamCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new favourite team for the current user.
    """
    # Check if the team exists in the team_info table
    # Note: We don't need to check organization access for teams as they are global
    team_exists = db.query(models.TeamInfo).filter(
        models.TeamInfo.teamId == favourite_team_in.teamId
    ).first()
    
    if not team_exists:
        raise HTTPException(
            status_code=404, 
            detail="Team not found"
        )
    
    favourite_team = crud.favourite_teams.create_with_user(
        db=db, obj_in=favourite_team_in, user_id=current_user.id
    )
    return favourite_team


@router.get("/", response_model=List[FavouriteTeamRead])
def read_favourite_teams(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve favourite teams for the current user.
    """
    favourite_teams = crud.favourite_teams.get_by_user(
        db=db, user_id=current_user.id
    )
    return favourite_teams


@router.delete("/")
def delete_favourite_team(
    *,
    db: Session = Depends(deps.get_db),
    favourite_team_delete: FavouriteTeamDelete,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Remove a favourite team for the current user.
    """
    success = crud.favourite_teams.delete_by_user_and_team(
        db=db, 
        user_id=current_user.id, 
        teamId=favourite_team_delete.teamId
    )
    if not success:
        raise HTTPException(
            status_code=404, 
            detail="Favourite team not found"
        )
    return {"message": "Favourite team removed successfully"}


@router.get("/check/{teamId}")
def check_favourite_team(
    *,
    db: Session = Depends(deps.get_db),
    teamId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Check if a team is marked as favourite for the current user.
    """
    is_favourite = crud.favourite_teams.is_favourite_for_user(
        db=db, user_id=current_user.id, teamId=teamId
    )
    return {"is_favourite": is_favourite}
