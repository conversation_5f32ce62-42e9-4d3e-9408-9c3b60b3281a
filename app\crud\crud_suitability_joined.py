from app.crud.crud_base import CRUDBase
from app import models

from sqlalchemy import desc, or_, exists, func, cast, Float, Integer, literal, text
from sqlalchemy.orm import Session, lazyload, Load
from typing import Any, Optional, TypeVar
from app.db.base_class import Base
from sqlalchemy import desc
from app.models import (
    User,
    PlayerInfo,
    CompTeamElos,
    CompElos,
    PlayerYearMinutes,
    TeamRequest,
    TeamInfo,
    SourceToRecord,
    Contact,
    TrackedTransfers,
    Organization,
    AssignedToRecord,
    CommunityProposal,
    CommunityDeal,
)
from sqlalchemy.sql import func, desc
from sqlalchemy import text, literal, case, nullslast, and_, select, literal_column
from sqlalchemy.dialects.postgresql import Any as AnyPG, array_agg
from app.utils.matching_helpers import get_window_from_date
from app.utils import control_stages
from app.api import utils

ModelType = TypeVar("ModelType", bound=Base)

# TIME WINDOW
months_ago = func.now() - text("INTERVAL '10 months'")


class CRUDSuitabilityJoined(CRUDBase):
    def get_matches_for_request(
        self,
        db: Session,
        org_id: Any,
        request_id: Any,
        active: bool,
        control_stage_list: Any,
        current_user=None,
    ) -> Optional[ModelType]:

        agent_info_subq = (
            db.query(
                TeamRequest.teamId,
                TeamRequest.position,
                TeamRequest.transfer_period,
                func.jsonb_build_object(
                    "agency",
                    Organization.name,
                    "id",
                    User.id,
                    "has_player_in_club",
                    exists(
                        select(literal(1)).where(
                            and_(
                                PlayerInfo.agent_id == Organization.agency_id,
                                PlayerInfo.currentTeamId == TeamRequest.teamId,
                            )
                        )
                    ),
                    "has_propose_badge",
                    and_(
                        select(func.count(func.distinct(CommunityProposal.player_id)))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        select(func.count(CommunityProposal.id))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                    ),
                    "has_uploaders_badge",
                    and_(
                        select(func.count(TeamRequest.id))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 10,
                        select(func.count(func.distinct(TeamRequest.teamId)))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                    ),
                    "has_all_rounder",
                    and_(
                        # Proposals
                        select(func.count(func.distinct(CommunityProposal.player_id)))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        select(func.count(CommunityProposal.id))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        # Uploads
                        select(func.count(TeamRequest.id))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        select(func.count(func.distinct(TeamRequest.teamId)))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        # Feedback ratio
                        case(
                            (
                                select(func.count(CommunityDeal.id))
                                .where(
                                    CommunityDeal.organization_id == Organization.id,
                                    CommunityDeal.type == "received",
                                    CommunityDeal.created_at >= months_ago,
                                    CommunityDeal.feedback != None,
                                )
                                .correlate(Organization)
                                .as_scalar()
                                >= 2,
                                True,
                            ),
                            else_=False,
                        )
                        & (
                            (
                                cast(
                                    select(func.count(CommunityDeal.id))
                                    .where(
                                        CommunityDeal.organization_id
                                        == Organization.id,
                                        CommunityDeal.type == "received",
                                        CommunityDeal.created_at >= months_ago,
                                        CommunityDeal.feedback != None,
                                    )
                                    .correlate(Organization)
                                    .as_scalar(),
                                    Float,
                                )
                                / cast(
                                    select(func.count(CommunityDeal.id))
                                    .where(
                                        CommunityDeal.organization_id
                                        == Organization.id,
                                        CommunityDeal.type == "received",
                                        CommunityDeal.created_at >= months_ago,
                                    )
                                    .correlate(Organization)
                                    .as_scalar(),
                                    Float,
                                )
                            )
                            >= 0.3
                        ),
                    ),
                ).label("agent_info"),
            )
            .join(User, TeamRequest.created_by == User.id)
            .join(Organization, User.organization_id == Organization.id)
            .filter(
                TeamRequest.is_community == True,
                TeamRequest.status.in_(["open", "maybe"]),
            )
            .subquery()
        )

        agent_count_qry = (
            db.query(
                agent_info_subq.c.teamId,
                agent_info_subq.c.position,
                agent_info_subq.c.transfer_period,
                func.count().label("count_duplicates"),
                func.array_agg(agent_info_subq.c.agent_info).label("created_by_info"),
            )
            .group_by(
                agent_info_subq.c.teamId,
                agent_info_subq.c.position,
                agent_info_subq.c.transfer_period,
            )
            .subquery()
        )

        team_qry = (
            db.query(
                TeamInfo.officialName,
                TeamInfo.area_name,
                CompTeamElos.rating,
                CompTeamElos.name.label("league_name"),
                TeamRequest.organization_id,
                TeamRequest.id,
                TeamRequest.position,
                TeamRequest.teamId,
                TeamRequest.max_age,
                CompTeamElos.competitionId,
                TeamRequest.is_community,
                agent_count_qry.c.count_duplicates,
                agent_count_qry.c.created_by_info,
                CompTeamElos.category.label("team_category"),
            )
            .join(TeamRequest, TeamRequest.teamId == CompTeamElos.teamId)
            .join(TeamInfo, TeamRequest.teamId == TeamInfo.teamId)
            .join(
                agent_count_qry,
                and_(
                    TeamRequest.teamId == agent_count_qry.c.teamId,
                    TeamRequest.position == agent_count_qry.c.position,
                    TeamRequest.transfer_period == agent_count_qry.c.transfer_period,
                    # TeamRequest.created_by == agent_count_qry.c.created_by,
                ),
                isouter=True,
            )
            .filter((TeamRequest.id == request_id))
            .subquery()
        )

        # Top 5 players average value subquery - this is the crucial part!
        # Calculate average value of the 5 most expensive players for each team
        top5_avg_subq = (
            text(
                """
                SELECT
                    team_id,
                    AVG(current_value) as top5_avg_value
                FROM (
                    SELECT
                        "currentTeamId" as team_id,
                        current_value,
                        ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
                    FROM wyscout.player_info2
                    WHERE current_value IS NOT NULL
                ) ranked
                WHERE rn <= 5
                GROUP BY team_id
                """
            )
            .columns(team_id=Integer, top5_avg_value=Float)
            .alias("top5_avg")
        )

        comp_qry = (
            db.query(
                CompElos.competitionId,
                CompElos.comp_median_rating,
                team_qry.c.league_name,
                team_qry.c.position,
                team_qry.c.teamId,
                team_qry.c.rating,
                team_qry.c.count_duplicates,
                team_qry.c.created_by_info,
                team_qry.c.team_category,
                case(
                    (
                        (team_qry.c.is_community)
                        & (team_qry.c.organization_id != org_id),
                        True,
                    ),
                    else_=False,
                ).label("is_community"),
            )
            .add_column(top5_avg_subq.c.top5_avg_value.label("top5_avg_value"))
            .join(team_qry, CompElos.competitionId == team_qry.c.competitionId)
            .join(
                top5_avg_subq,
                top5_avg_subq.c.team_id == team_qry.c.teamId,
                isouter=True,
            )
            .subquery()
        )

        player_min_suit = (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        ) * (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        )
        comp_elo_suit = (
            1
            - func.least(
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating,
                1,
            )
        ) * (
            1
            - (
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating
            )
        )
        team_elo_suit = (
            1
            - func.least(
                func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating, 1
            )
        ) * (
            1 - (func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating)
        )
        # Financial suitability calculation using top 5 most valuable players
        financial_suit = case(
            (
                and_(
                    PlayerInfo.current_value.isnot(None),
                    PlayerInfo.current_value > 0,
                    comp_qry.c.top5_avg_value.isnot(None),
                    comp_qry.c.top5_avg_value > 0,
                ),
                func.least(1.0, comp_qry.c.top5_avg_value / PlayerInfo.current_value),
            ),
            else_=1.0,
        )

        # Updated suitability score with financial component (weights: 0.65, 0.25, 0.10)
        base_suit_score = (
            0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit
        ) * (0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit)

        # Apply financial suitability factor
        suit_score = base_suit_score * (1 - 0.6 * (1 - financial_suit))
        suit_rating = case(
            (suit_score < 0.1, 0),
            ((suit_score >= 0.1) & (suit_score < 0.2), 1.0),
            ((suit_score >= 0.2) & (suit_score < 0.3), 1.5),
            ((suit_score >= 0.3) & (suit_score < 0.4), 2.0),
            ((suit_score >= 0.4) & (suit_score < 0.5), 2.5),
            ((suit_score >= 0.5) & (suit_score < 0.6), 3.0),
            ((suit_score >= 0.6) & (suit_score < 0.7), 3.5),
            ((suit_score >= 0.7) & (suit_score < 0.8), 4),
            ((suit_score >= 0.8) & (suit_score < 0.9), 4.5),
            ((suit_score >= 0.9) & (suit_score <= 1), 5),
            else_=None,
        )

        assigned_subquery = (
            db.query(
                func.json_agg(
                    func.json_build_object(
                        "contact",
                        func.json_build_object(
                            "id",
                            Contact.id,
                            "first_name",
                            Contact.first_name,
                            "last_name",
                            Contact.last_name,
                            "contact_organization",
                            Contact.contact_organization,
                        ),
                        "contact_id",
                        AssignedToRecord.contact_id,
                    )
                )
            )
            .join(Contact, AssignedToRecord.contact_id == Contact.id)
            .filter(AssignedToRecord.player_id == self.model.id)
            .correlate(self.model)
            .as_scalar()
        )
        final_query_befor_filters = (
            db.query(
                PlayerInfo.team_name,
                case(
                    (comp_qry.c.is_community == True, None),
                    else_=team_qry.c.officialName,
                ).label("team_name"),
                case(
                    (comp_qry.c.is_community == True, None),
                    else_=team_qry.c.officialName,
                ).label("request_name"),
                comp_qry.c.league_name,
                team_qry.c.area_name,
                team_qry.c.max_age,
                self.model.playerId,
                self.model.id.label("player_id"),
                literal(request_id).label("request_id"),
                PlayerInfo.fullName,
                PlayerInfo.shortName,
                PlayerInfo.firstName,
                PlayerInfo.lastName,
                PlayerInfo.birthDate,
                PlayerInfo.imageDataURL,
                PlayerInfo.birthArea_name.label("birth_area"),
                PlayerInfo.passport,
                PlayerInfo.eu,
                PlayerInfo.date_joined_current_team,
                PlayerInfo.contract_expiry,
                PlayerInfo.height,
                self.model.quality,
                self.model.potential,
                self.model.control_stage,
                self.model.transfer_strategy,
                self.model.description,
                self.model.expected_net_salary,
                PlayerInfo.foot,
                PlayerInfo.player_role,
                PlayerInfo.agent,
                PlayerInfo.player_url,
                self.model.last_updated,
                PlayerInfo.primary_ws_position,
                PlayerInfo.secondary_ws_position,
                PlayerInfo.birthDate,
                PlayerInfo.team_name,
                PlayerInfo.passport,
                comp_qry.c.is_community,
                PlayerInfo.tm_link,
                PlayerInfo.current_value.label("tm_value"),
                comp_qry.c.teamId.label("hiring_team_id"),
                self.model.position.label("player_pos"),
                comp_qry.c.position.label("requested_pos"),
                self.model.club_asking_price,
                self.model.current_gross_salary,
                CompTeamElos.rating,
                comp_qry.c.rating.label("hiring_team_rating"),
                CompElos.comp_median_rating,
                comp_qry.c.comp_median_rating.label("hiring_comp_rating"),
                PlayerYearMinutes.avg_mins_in_year,
                comp_qry.c.count_duplicates,
                comp_qry.c.created_by_info,
                assigned_subquery.label("assigned_to_record"),
                CompTeamElos.category.label("player_team_category"),
                comp_qry.c.team_category,
            )
            .add_column(team_elo_suit.label("team_elo_suitability"))
            .add_column(comp_elo_suit.label("comp_elo_suitability"))
            .add_column(player_min_suit.label("player_min_suitability"))
            .add_column(financial_suit.label("financial_suitability"))
            .add_column(comp_qry.c.top5_avg_value.label("top5_team_avg_value"))
            .add_column(suit_score.label("suitability_score"))
            .add_column(suit_rating.label("suitability_rating"))
            .join(PlayerInfo, self.model.playerId == PlayerInfo.playerId, isouter=True)
            .join(
                PlayerYearMinutes,
                self.model.playerId == PlayerYearMinutes.playerId,
                isouter=True,
            )
            .join(
                CompTeamElos,
                PlayerInfo.currentTeamId == CompTeamElos.teamId,
                isouter=True,
            )
            .join(
                CompElos,
                CompElos.competitionId == CompTeamElos.competitionId,
                isouter=True,
            )
            .join(comp_qry, literal(1) == 1)
            .filter(
                or_(
                    (
                        comp_qry.c.position == func.any(self.model.position)
                    ),  # Direct match
                    and_(comp_qry.c.position == "rb", self.model.position.any("rwb")),
                    and_(comp_qry.c.position == "rwb", self.model.position.any("rb")),
                    and_(comp_qry.c.position == "lb", self.model.position.any("lwb")),
                    and_(comp_qry.c.position == "lwb", self.model.position.any("lb")),
                    and_(comp_qry.c.position == "lcb", self.model.position.any("cb")),
                    and_(comp_qry.c.position == "rcb", self.model.position.any("cb")),
                )
                & (
                    (team_qry.c.organization_id == org_id)
                    | (team_qry.c.is_community == True)
                )
                & (self.model.organization_id == org_id)
                # Add the category filtering condition
                & ~(
                    and_(
                        CompTeamElos.category == "default",
                        comp_qry.c.team_category != "default",
                    )
                )
            )
            .order_by(nullslast(desc(suit_score)))
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
        )
        if active is not None:
            if active:
                final_query_befor_filters = final_query_befor_filters.filter(
                    self.model.control_stage.in_(control_stages)
                )
            else:
                final_query_befor_filters = final_query_befor_filters.filter(
                    self.model.control_stage == "closed"
                )
        if control_stage_list:
            final_query_befor_filters = final_query_befor_filters.filter(
                self.model.control_stage.in_(control_stage_list)
            )

        results = final_query_befor_filters.all()

        if current_user:
            exclude = utils.exclude_based_on_role(current_user)
            if exclude:
                results = [r for r in results if exclude(r, current_user)]
            else:
                results = [
                    r for r in results if r.control_stage not in ["closed", "archived"]
                ]

        return [u._asdict() for u in results]

    def get_matches_for_team(
        self, db: Session, org_id: Any, team_id: Any
    ) -> Optional[ModelType]:
        team_qry = (
            db.query(
                TeamInfo.officialName,
                TeamInfo.teamId,
                TeamInfo.area_name,
                CompTeamElos.rating,
                CompTeamElos.competitionId,
                CompTeamElos.category.label("team_category"),
            )
            .join(CompTeamElos, TeamInfo.teamId == CompTeamElos.teamId)
            .filter((TeamInfo.teamId == team_id))
            .subquery()
        )

        # Top 5 players average value subquery - this is the crucial part!
        # Calculate average value of the 5 most expensive players for each team
        top5_avg_subq = (
            text(
                """
                SELECT
                    team_id,
                    AVG(current_value) as top5_avg_value
                FROM (
                    SELECT
                        "currentTeamId" as team_id,
                        current_value,
                        ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
                    FROM wyscout.player_info2
                    WHERE current_value IS NOT NULL
                ) ranked
                WHERE rn <= 5
                GROUP BY team_id
                """
            )
            .columns(team_id=Integer, top5_avg_value=Float)
            .alias("top5_avg")
        )

        comp_qry = (
            db.query(
                CompElos.competitionId,
                CompElos.comp_median_rating,
                team_qry.c.teamId,
                team_qry.c.rating,
                team_qry.c.team_category,
            )
            .add_column(top5_avg_subq.c.top5_avg_value.label("top5_avg_value"))
            .join(team_qry, CompElos.competitionId == team_qry.c.competitionId)
            .join(
                top5_avg_subq,
                top5_avg_subq.c.team_id == team_qry.c.teamId,
                isouter=True,
            )
            .subquery()
        )

        player_min_suit = (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        ) * (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        )
        comp_elo_suit = (
            1
            - func.least(
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating,
                1,
            )
        ) * (
            1
            - (
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating
            )
        )
        team_elo_suit = (
            1
            - func.least(
                func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating, 1
            )
        ) * (
            1 - (func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating)
        )
        # Financial suitability calculation using top 5 most valuable players
        financial_suit = case(
            (
                and_(
                    PlayerInfo.current_value.isnot(None),
                    PlayerInfo.current_value > 0,
                    comp_qry.c.top5_avg_value.isnot(None),
                    comp_qry.c.top5_avg_value > 0,
                ),
                func.least(1.0, comp_qry.c.top5_avg_value / PlayerInfo.current_value),
            ),
            else_=1.0,
        )

        base_suit_score = (
            0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit
        ) * (0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit)

        # Apply financial suitability factor
        suit_score = base_suit_score * (1 - 0.6 * (1 - financial_suit))

        suit_rating = case(
            (suit_score < 0.1, 0),
            ((suit_score >= 0.1) & (suit_score < 0.2), 1.0),
            ((suit_score >= 0.2) & (suit_score < 0.3), 1.5),
            ((suit_score >= 0.3) & (suit_score < 0.4), 2.0),
            ((suit_score >= 0.4) & (suit_score < 0.5), 2.5),
            ((suit_score >= 0.5) & (suit_score < 0.6), 3.0),
            ((suit_score >= 0.6) & (suit_score < 0.7), 3.5),
            ((suit_score >= 0.7) & (suit_score < 0.8), 4),
            ((suit_score >= 0.8) & (suit_score < 0.9), 4.5),
            ((suit_score >= 0.9) & (suit_score <= 1), 5),
            else_=None,
        )

        return (
            db.query(
                PlayerInfo.playerId,
                PlayerInfo.fullName,
                PlayerInfo.shortName,
                PlayerInfo.primary_ws_position,
                PlayerInfo.secondary_ws_position,
                PlayerInfo.firstName,
                PlayerInfo.lastName,
                PlayerInfo.birthDate,
                PlayerInfo.team_name,
                PlayerInfo.birthArea_name.label("birth_area"),
                PlayerInfo.passport,
                PlayerInfo.eu,
                PlayerInfo.tm_link,
                PlayerInfo.imageDataURL,
                PlayerInfo.current_value.label("tm_value"),
                PlayerInfo.foot,
                PlayerInfo.contract_expiry,
                comp_qry.c.teamId.label("hiring_team_id"),
                self.model.club_asking_price,
                self.model.current_gross_salary,
                self.model.description,
                CompTeamElos.rating,
                comp_qry.c.rating.label("hiring_team_rating"),
                CompElos.comp_median_rating,
                comp_qry.c.comp_median_rating.label("hiring_comp_rating"),
                PlayerYearMinutes.avg_mins_in_year,
            )
            .add_column(team_elo_suit.label("team_elo_suitability"))
            .add_column(comp_elo_suit.label("comp_elo_suitability"))
            .add_column(player_min_suit.label("player_min_suitability"))
            .add_column(financial_suit.label("financial_suitability"))
            .add_column(comp_qry.c.top5_avg_value.label("top5_team_avg_value"))
            .add_column(suit_score.label("suitability_score_w_min_coalesce"))
            .add_column(suit_rating.label("suitability_rating"))
            .join(PlayerInfo, self.model.playerId == PlayerInfo.playerId, isouter=True)
            .join(
                PlayerYearMinutes,
                self.model.playerId == PlayerYearMinutes.playerId,
                isouter=True,
            )
            .join(
                CompTeamElos,
                PlayerInfo.currentTeamId == CompTeamElos.teamId,
                isouter=True,
            )
            .join(
                CompElos,
                CompElos.competitionId == CompTeamElos.competitionId,
                isouter=True,
            )
            .join(comp_qry, literal(1) == 1)
            .filter(
                ((self.model.organization_id == org_id))
                & (suit_rating >= 3)
                & (comp_qry.c.teamId != PlayerInfo.currentTeamId)
                & or_(
                    self.model.transfer_strategy != "retain",
                    self.model.transfer_strategy == None,
                )
                # Add the category filtering condition
                & ~(
                    and_(
                        CompTeamElos.category == "default",
                        comp_qry.c.team_category != "default",
                    )
                )
            )
            .order_by(nullslast(desc(suit_score)))
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .all()
        )

    def get_player_team_suitability(
        self, db: Session, player_id: Any, team_id: Any
    ) -> Optional[ModelType]:
        team_qry = (
            db.query(
                TeamInfo.officialName,
                TeamInfo.teamId,
                TeamInfo.area_name,
                CompTeamElos.rating,
                CompTeamElos.competitionId,
                CompTeamElos.category.label("team_category"),
            )
            .join(CompTeamElos, TeamInfo.teamId == CompTeamElos.teamId)
            .filter((TeamInfo.teamId == team_id))
            .subquery()
        )

        # Top 5 players average value subquery - this is the crucial part!
        # Calculate average value of the 5 most expensive players for each team
        top5_avg_subq = (
            text(
                """
                SELECT
                    team_id,
                    AVG(current_value) as top5_avg_value
                FROM (
                    SELECT
                        "currentTeamId" as team_id,
                        current_value,
                        ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
                    FROM wyscout.player_info2
                    WHERE current_value IS NOT NULL
                ) ranked
                WHERE rn <= 5
                GROUP BY team_id
                """
            )
            .columns(team_id=Integer, top5_avg_value=Float)
            .alias("top5_avg")
        )

        comp_qry = (
            db.query(
                CompElos.competitionId,
                CompElos.comp_median_rating,
                team_qry.c.teamId,
                team_qry.c.rating,
                team_qry.c.team_category,
            )
            .add_column(top5_avg_subq.c.top5_avg_value.label("top5_avg_value"))
            .join(team_qry, CompElos.competitionId == team_qry.c.competitionId)
            .join(
                top5_avg_subq,
                top5_avg_subq.c.team_id == team_qry.c.teamId,
                isouter=True,
            )
            .subquery()
        )

        player_min_suit = (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        ) * (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        )
        comp_elo_suit = (
            1
            - func.least(
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating,
                1,
            )
        ) * (
            1
            - (
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating
            )
        )
        team_elo_suit = (
            1
            - func.least(
                func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating, 1
            )
        ) * (
            1 - (func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating)
        )

        # Financial suitability calculation using top 5 most valuable players
        financial_suit = case(
            (
                and_(
                    PlayerInfo.current_value.isnot(None),
                    PlayerInfo.current_value > 0,
                    comp_qry.c.top5_avg_value.isnot(None),
                    comp_qry.c.top5_avg_value > 0,
                ),
                func.least(1.0, comp_qry.c.top5_avg_value / PlayerInfo.current_value),
            ),
            else_=1.0,
        )

        base_suit_score = (
            0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit
        ) * (0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit)

        # Apply financial suitability factor
        suit_score = base_suit_score * (1 - 0.6 * (1 - financial_suit))

        return (
            db.query(PlayerInfo.playerId)
            .add_column(financial_suit.label("financial_suitability"))
            .add_column(comp_qry.c.top5_avg_value.label("top5_team_avg_value"))
            .add_column(suit_score.label("suitability_score_w_min_coalesce"))
            .join(PlayerInfo, self.model.playerId == PlayerInfo.playerId, isouter=True)
            .join(
                PlayerYearMinutes,
                self.model.playerId == PlayerYearMinutes.playerId,
                isouter=True,
            )
            .join(
                CompTeamElos,
                PlayerInfo.currentTeamId == CompTeamElos.teamId,
                isouter=True,
            )
            .join(
                CompElos,
                CompElos.competitionId == CompTeamElos.competitionId,
                isouter=True,
            )
            .join(comp_qry, literal(1) == 1)
            .filter(
                ((self.model.playerId == player_id))
                # Add the category filtering condition
                & ~(
                    and_(
                        CompTeamElos.category == "default",
                        comp_qry.c.team_category != "default",
                    )
                )
            )
            .order_by(nullslast(desc(suit_score)))
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .all()
        )

    def get_team_suitability(
        self, db: Session, org_id: Any, team_id: Any, can_access_sensitive: bool
    ) -> Optional[ModelType]:
        filter_cond = (
            self.model.is_sensitive != True if not can_access_sensitive else True
        )

        team_qry = (
            db.query(
                TeamInfo.officialName,
                TeamInfo.teamId,
                TeamInfo.area_name,
                CompTeamElos.rating,
                CompTeamElos.competitionId,
                CompTeamElos.category.label("team_category"),
            )
            .join(CompTeamElos, TeamInfo.teamId == CompTeamElos.teamId)
            .filter((TeamInfo.teamId == team_id))
            .subquery()
        )

        # Top 5 players average value subquery - this is the crucial part!
        # Calculate average value of the 5 most expensive players for each team
        top5_avg_subq = (
            text(
                """
                SELECT
                    team_id,
                    AVG(current_value) as top5_avg_value
                FROM (
                    SELECT
                        "currentTeamId" as team_id,
                        current_value,
                        ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
                    FROM wyscout.player_info2
                    WHERE current_value IS NOT NULL
                ) ranked
                WHERE rn <= 5
                GROUP BY team_id
                """
            )
            .columns(team_id=Integer, top5_avg_value=Float)
            .alias("top5_avg")
        )

        comp_qry = (
            db.query(
                CompElos.competitionId,
                CompElos.comp_median_rating,
                team_qry.c.teamId,
                team_qry.c.rating,
                team_qry.c.team_category,
            )
            .add_column(top5_avg_subq.c.top5_avg_value.label("top5_avg_value"))
            .join(team_qry, CompElos.competitionId == team_qry.c.competitionId)
            .join(
                top5_avg_subq,
                top5_avg_subq.c.team_id == team_qry.c.teamId,
                isouter=True,
            )
            .subquery()
        )

        player_min_suit = (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        ) * (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        )
        comp_elo_suit = (
            1
            - func.least(
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating,
                1,
            )
        ) * (
            1
            - (
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating
            )
        )
        team_elo_suit = (
            1
            - func.least(
                func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating, 1
            )
        ) * (
            1 - (func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating)
        )
        # Financial suitability calculation using top 5 most valuable players
        financial_suit = case(
            (
                and_(
                    PlayerInfo.current_value.isnot(None),
                    PlayerInfo.current_value > 0,
                    comp_qry.c.top5_avg_value.isnot(None),
                    comp_qry.c.top5_avg_value > 0,
                ),
                func.least(1.0, comp_qry.c.top5_avg_value / PlayerInfo.current_value),
            ),
            else_=1.0,
        )

        # Updated suitability score with financial component (weights: 0.65, 0.25, 0.10)
        base_suit_score = (
            0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit
        ) * (0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit)

        # Apply financial suitability factor
        suit_score = base_suit_score * (1 - 0.6 * (1 - financial_suit))
        suit_rating = case(
            (suit_score < 0.1, 0),
            ((suit_score >= 0.1) & (suit_score < 0.2), 1.0),
            ((suit_score >= 0.2) & (suit_score < 0.3), 1.5),
            ((suit_score >= 0.3) & (suit_score < 0.4), 2.0),
            ((suit_score >= 0.4) & (suit_score < 0.5), 2.5),
            ((suit_score >= 0.5) & (suit_score < 0.6), 3.0),
            ((suit_score >= 0.6) & (suit_score < 0.7), 3.5),
            ((suit_score >= 0.7) & (suit_score < 0.8), 4),
            ((suit_score >= 0.8) & (suit_score < 0.9), 4.5),
            ((suit_score >= 0.9) & (suit_score <= 1), 5),
            else_=None,
        )

        query = (
            db.query(self.model)
            .add_column(financial_suit.label("financial_suitability"))
            .add_column(comp_qry.c.top5_avg_value.label("top5_team_avg_value"))
            .add_column(suit_score.label("suitability_score"))
            .add_column(suit_rating.label("suitability_rating"))
            .join(PlayerInfo, self.model.playerId == PlayerInfo.playerId, isouter=True)
            .join(
                PlayerYearMinutes,
                self.model.playerId == PlayerYearMinutes.playerId,
                isouter=True,
            )
            .join(
                CompTeamElos,
                PlayerInfo.currentTeamId == CompTeamElos.teamId,
                isouter=True,
            )
            .join(
                CompElos,
                CompElos.competitionId == CompTeamElos.competitionId,
                isouter=True,
            )
            .join(comp_qry, literal(1) == 1)
            .filter(
                ((self.model.organization_id == org_id)),
                filter_cond,
                # Add the category filtering condition
                ~(
                    and_(
                        CompTeamElos.category == "default",
                        comp_qry.c.team_category != "default",
                    )
                ),
            )
            .order_by(nullslast(desc(suit_score)))
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .all()
        )
        return [u._asdict() for u in query]

    def get_matches_for_player(
        self,
        db: Session,
        org_id: Any,
        player_id: Any,
        #    window:Any
    ) -> Optional[ModelType]:
        player_qry = (
            db.query(
                self.model,
                PlayerInfo.currentTeamId,
                CompTeamElos.rating,
                CompElos.comp_median_rating,
                PlayerInfo.currentTeamId,
                PlayerInfo.fullName,
                PlayerInfo.firstName,
                PlayerInfo.lastName,
                PlayerInfo.shortName,
                PlayerYearMinutes.avg_mins_in_year,
                PlayerInfo.current_value,
                CompTeamElos.category.label("player_team_category"),
            )
            .join(PlayerInfo, self.model.playerId == PlayerInfo.playerId)
            .filter(self.model.id == player_id)
            .join(
                CompTeamElos,
                PlayerInfo.currentTeamId == CompTeamElos.teamId,
                isouter=True,
            )
            .join(
                CompElos,
                CompTeamElos.competitionId == CompElos.competitionId,
                isouter=True,
            )
            .join(
                PlayerYearMinutes,
                self.model.playerId == PlayerYearMinutes.playerId,
                isouter=True,
            )
            .subquery()
        )

        source_qry = (
            db.query(
                TeamRequest.id,
                SourceToRecord.source_id,
                Contact.email,
                Contact.contact_organization,
            )
            .join(SourceToRecord, SourceToRecord.team_request_id == TeamRequest.id)
            .distinct(TeamRequest.id)
            .order_by(TeamRequest.id)
            .join(Contact, SourceToRecord.source_id == Contact.id)
            .subquery()
        )

        agent_info_subquery = (
            db.query(
                TeamRequest.teamId,
                TeamRequest.position,
                TeamRequest.transfer_period,
                func.jsonb_build_object(
                    "agency",
                    Organization.name,
                    "id",
                    User.id,
                    "has_player_in_club",
                    exists(
                        select(literal(1)).where(
                            and_(
                                PlayerInfo.agent_id == Organization.agency_id,
                                PlayerInfo.currentTeamId == TeamRequest.teamId,
                            )
                        )
                    ),
                    "has_propose_badge",
                    and_(
                        select(func.count(func.distinct(CommunityProposal.player_id)))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        select(func.count(CommunityProposal.id))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                    ),
                    "has_uploaders_badge",
                    and_(
                        select(func.count(TeamRequest.id))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 10,
                        select(func.count(func.distinct(TeamRequest.teamId)))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                    ),
                    "has_all_rounder",
                    and_(
                        # Proposals
                        select(func.count(func.distinct(CommunityProposal.player_id)))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        select(func.count(CommunityProposal.id))
                        .where(
                            CommunityProposal.organization_id == Organization.id,
                            CommunityProposal.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        # Uploads
                        select(func.count(TeamRequest.id))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        select(func.count(func.distinct(TeamRequest.teamId)))
                        .where(
                            TeamRequest.organization_id == Organization.id,
                            TeamRequest.is_community == True,
                            TeamRequest.created_at >= months_ago,
                        )
                        .correlate(Organization)
                        .as_scalar()
                        >= 2,
                        # Feedback ratio
                        case(
                            (
                                select(func.count(CommunityDeal.id))
                                .where(
                                    CommunityDeal.organization_id == Organization.id,
                                    CommunityDeal.type == "received",
                                    CommunityDeal.created_at >= months_ago,
                                    CommunityDeal.feedback != None,
                                )
                                .correlate(Organization)
                                .as_scalar()
                                >= 2,
                                True,
                            ),
                            else_=False,
                        )
                        & (
                            (
                                cast(
                                    select(func.count(CommunityDeal.id))
                                    .where(
                                        CommunityDeal.organization_id
                                        == Organization.id,
                                        CommunityDeal.type == "received",
                                        CommunityDeal.created_at >= months_ago,
                                        CommunityDeal.feedback != None,
                                    )
                                    .correlate(Organization)
                                    .as_scalar(),
                                    Float,
                                )
                                / cast(
                                    select(func.count(CommunityDeal.id))
                                    .where(
                                        CommunityDeal.organization_id
                                        == Organization.id,
                                        CommunityDeal.type == "received",
                                        CommunityDeal.created_at >= months_ago,
                                    )
                                    .correlate(Organization)
                                    .as_scalar(),
                                    Float,
                                )
                            )
                            >= 0.3
                        ),
                    ),
                ).label("agent_info"),
            )
            .join(User, TeamRequest.created_by == User.id)
            .join(Organization, User.organization_id == Organization.id)
            .filter(TeamRequest.is_community == True)
            .filter(TeamRequest.status.in_(["open", "maybe"]))
            .subquery()
        )

        # Final aggregate with JSON array
        agent_count_qry = (
            db.query(
                agent_info_subquery.c.teamId,
                agent_info_subquery.c.position,
                agent_info_subquery.c.transfer_period,
                func.count().label("count_duplicates"),
                func.array_agg(agent_info_subquery.c.agent_info).label(
                    "created_by_info"
                ),
            ).group_by(
                agent_info_subquery.c.teamId,
                agent_info_subquery.c.position,
                agent_info_subquery.c.transfer_period,
            )
        ).subquery()

        tracked_transfers_subquery = (
            db.query(
                TrackedTransfers.request_id,
                func.json_agg(
                    func.json_build_object(
                        "transfer_id",
                        TrackedTransfers.id,
                        "position",
                        TrackedTransfers.position,
                        "player_name",
                        TrackedTransfers.player_name,
                        "left_name",
                        TrackedTransfers.left_name,
                        "fee",
                        TrackedTransfers.fee,
                        "date",
                        TrackedTransfers.date,
                        "player_url",
                        TrackedTransfers.player_url,
                        "active",
                        TrackedTransfers.active,
                        "request_id",
                        TrackedTransfers.request_id,
                    )
                ).label("tracked_transfer"),
            )
            .group_by(TrackedTransfers.request_id)
            .subquery()
        )

        # Top 5 players average value subquery - this is the crucial part!
        # Calculate average value of the 5 most expensive players for each team
        top5_avg_subq = (
            text(
                """
                SELECT
                    team_id,
                    AVG(current_value) as top5_avg_value
                FROM (
                    SELECT
                        "currentTeamId" as team_id,
                        current_value,
                        ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
                    FROM wyscout.player_info2
                    WHERE current_value IS NOT NULL
                ) ranked
                WHERE rn <= 5
                GROUP BY team_id
                """
            )
            .columns(team_id=Integer, top5_avg_value=Float)
            .alias("top5_avg")
        )

        team_qry = (
            db.query(
                TeamRequest.id,
                source_qry.c.source_id,
                TeamInfo.name.label("officialName"),
                TeamInfo.area_name,
                TeamInfo.imageDataURL,
                CompTeamElos.name.label("league_name"),
                CompTeamElos.rating,
                CompTeamElos.divisionLevel,
                TeamRequest.max_value,
                TeamRequest.max_net_salary,
                TeamRequest.position,
                TeamRequest.last_updated,
                TeamRequest.teamId,
                TeamRequest.organization_id,
                TeamRequest.status,
                CompTeamElos.name,
                TeamRequest.transfer_period,
                TeamRequest.type,
                CompTeamElos.competitionId,
                TeamRequest.is_community,
                TeamRequest.foot,
                agent_count_qry.c.count_duplicates,
                agent_count_qry.c.created_by_info,
                CompTeamElos.category.label("team_category"),
                func.concat(
                    source_qry.c.email,
                    literal(", "),
                    source_qry.c.contact_organization,
                    literal(": "),
                ).label("source"),
                tracked_transfers_subquery.c.tracked_transfer,
            )
            .join(CompTeamElos, TeamRequest.teamId == CompTeamElos.teamId, isouter=True)
            .join(TeamInfo, TeamInfo.teamId == TeamRequest.teamId, isouter=True)
            .join(
                tracked_transfers_subquery,
                TeamRequest.id == tracked_transfers_subquery.c.request_id,
                isouter=True,
            )
            .join(
                agent_count_qry,
                and_(
                    TeamRequest.teamId == agent_count_qry.c.teamId,
                    TeamRequest.position == agent_count_qry.c.position,
                    TeamRequest.transfer_period == agent_count_qry.c.transfer_period,
                    TeamRequest.transfer_period.contains([get_window_from_date()]),
                    # TeamRequest.created_by == agent_count_qry.c.created_by,
                ),
                isouter=True,
            )
            .join(source_qry, source_qry.c.id == TeamRequest.id, isouter=True)
            .filter(
                (
                    (TeamRequest.organization_id == org_id)
                    | (TeamRequest.is_community == literal(True))
                )
                & TeamRequest.transfer_period.contains([get_window_from_date()])
                & TeamRequest.status.in_(["open", "maybe"])
            )
            .subquery()
        )

        comp_qry = (
            db.query(
                team_qry.c.id,
                CompElos.competitionId,
                CompElos.comp_median_rating,
                team_qry.c.league_name,
                team_qry.c.imageDataURL,
                team_qry.c.transfer_period,
                team_qry.c.officialName,
                team_qry.c.name,
                team_qry.c.area_name,
                team_qry.c.type,
                team_qry.c.divisionLevel,
                team_qry.c.source,
                team_qry.c.last_updated,
                team_qry.c.position,
                team_qry.c.teamId,
                team_qry.c.rating,
                team_qry.c.count_duplicates,
                team_qry.c.created_by_info,
                team_qry.c.status,
                team_qry.c.tracked_transfer,
                team_qry.c.foot,
                team_qry.c.team_category,
                case(
                    (
                        (team_qry.c.is_community)
                        & (team_qry.c.organization_id != org_id),
                        True,
                    ),
                    else_=False,
                ).label("is_community"),
                team_qry.c.transfer_period,
                team_qry.c.max_value,
                team_qry.c.max_net_salary,
            )
            .join(
                CompElos,
                CompElos.competitionId == team_qry.c.competitionId,
                isouter=True,
            )
            .join(
                top5_avg_subq,
                top5_avg_subq.c.team_id == team_qry.c.teamId,
                isouter=True,
            )
            .add_column(top5_avg_subq.c.top5_avg_value.label("top5_avg_value"))
            .subquery()
        )

        player_min_suit = (
            func.least(70.0, func.coalesce(player_qry.c.avg_mins_in_year, 0.0)) / 70.0
        ) * (func.least(70.0, func.coalesce(player_qry.c.avg_mins_in_year, 0.0)) / 70.0)
        comp_elo_suit = (
            1
            - func.least(
                func.abs(
                    player_qry.c.comp_median_rating - comp_qry.c.comp_median_rating
                )
                / comp_qry.c.comp_median_rating,
                1,
            )
        ) * (
            1
            - (
                func.abs(
                    player_qry.c.comp_median_rating - comp_qry.c.comp_median_rating
                )
                / comp_qry.c.comp_median_rating
            )
        )
        team_elo_suit = (
            1
            - func.least(
                func.abs(player_qry.c.rating - comp_qry.c.rating) / comp_qry.c.rating, 1
            )
        ) * (
            1 - (func.abs(player_qry.c.rating - comp_qry.c.rating) / comp_qry.c.rating)
        )
        # Financial suitability calculation using top 5 most valuable players
        financial_suit = case(
            (
                and_(
                    player_qry.c.current_value.isnot(None),
                    player_qry.c.current_value > 0,
                    comp_qry.c.top5_avg_value.isnot(None),
                    comp_qry.c.top5_avg_value > 0,
                ),
                func.least(1.0, comp_qry.c.top5_avg_value / player_qry.c.current_value),
            ),
            else_=1.0,
        )

        # Updated suitability score (weights: 0.45, 0.45, 0.10) with financial component
        base_suit_score = (
            0.45 * team_elo_suit + 0.35 * comp_elo_suit + 0.20 * player_min_suit
        ) * (0.45 * team_elo_suit + 0.35 * comp_elo_suit + 0.20 * player_min_suit)

        # Apply financial suitability factor
        suit_score = base_suit_score * (1 - 0.6 * (1 - financial_suit))
        suit_rating = case(
            (suit_score < 0.1, 0),
            ((suit_score >= 0.1) & (suit_score < 0.2), 1.0),
            ((suit_score >= 0.2) & (suit_score < 0.3), 1.5),
            ((suit_score >= 0.3) & (suit_score < 0.4), 2.0),
            ((suit_score >= 0.4) & (suit_score < 0.5), 2.5),
            ((suit_score >= 0.5) & (suit_score < 0.6), 3.0),
            ((suit_score >= 0.6) & (suit_score < 0.7), 3.5),
            ((suit_score >= 0.7) & (suit_score < 0.8), 4),
            ((suit_score >= 0.8) & (suit_score < 0.9), 4.5),
            ((suit_score >= 0.9) & (suit_score <= 1), 5),
            else_=None,
        )
        query = (
            db.query(
                case(
                    (comp_qry.c.is_community == True, None), else_=comp_qry.c.source
                ).label("source"),
                player_qry.c.id.label("player_id"),
                comp_qry.c.id.label("request_id"),
                comp_qry.c.last_updated,
                comp_qry.c.league_name,
                player_qry.c.fullName,
                player_qry.c.firstName,
                player_qry.c.lastName,
                player_qry.c.shortName,
                player_qry.c.current_value,
                comp_qry.c.foot,
                case(
                    (comp_qry.c.is_community == True, None),
                    else_=comp_qry.c.officialName,
                ).label("team_name"),
                comp_qry.c.name,
                comp_qry.c.imageDataURL,
                comp_qry.c.area_name,
                comp_qry.c.divisionLevel,
                comp_qry.c.type,
                comp_qry.c.max_value,
                comp_qry.c.max_net_salary,
                comp_qry.c.transfer_period,
                comp_qry.c.rating.label("request_team_rating"),
                comp_qry.c.count_duplicates,
                comp_qry.c.created_by_info,
                player_qry.c.rating.label("player_team_rating"),
                comp_qry.c.comp_median_rating.label("request_comp_rating"),
                player_qry.c.comp_median_rating.label("player_comp_rating"),
                comp_qry.c.teamId,
                player_qry.c.position.label("player_pos"),
                comp_qry.c.position.label("requested_pos"),
                comp_qry.c.is_community,
                comp_qry.c.status,
                comp_qry.c.tracked_transfer,
            )
            .add_column(team_elo_suit.label("team_elo_suitability"))
            .add_column(comp_elo_suit.label("comp_elo_suitability"))
            .add_column(player_min_suit.label("player_min_suitability"))
            .add_column(financial_suit.label("financial_suitability"))
            .add_column(comp_qry.c.top5_avg_value.label("top5_team_avg_value"))
            .add_column(suit_score.label("suitability_score"))
            .add_column(suit_rating.label("suitability_rating"))
            .filter(
                (
                    or_(
                        (
                            comp_qry.c.position == func.any(player_qry.c.position)
                        ),  # Direct match
                        and_(
                            comp_qry.c.position == "rb",
                            player_qry.c.position.any("rwb"),
                        ),
                        and_(
                            comp_qry.c.position == "rwb",
                            player_qry.c.position.any("rb"),
                        ),
                        and_(
                            comp_qry.c.position == "lb",
                            player_qry.c.position.any("lwb"),
                        ),
                        and_(
                            comp_qry.c.position == "lwb",
                            player_qry.c.position.any("lb"),
                        ),
                        and_(
                            comp_qry.c.position == "lcb",
                            player_qry.c.position.any("cb"),
                        ),
                        and_(
                            comp_qry.c.position == "rcb",
                            player_qry.c.position.any("cb"),
                        ),
                    )
                    & (
                        (player_qry.c.organization_id == org_id)
                        | (comp_qry.c.is_community == True)
                    )
                )
                & (
                    or_(
                        comp_qry.c.teamId != player_qry.c.currentTeamId,
                        player_qry.c.currentTeamId == None,
                    )
                )
                # Add the category filtering condition
                & ~(
                    and_(
                        player_qry.c.player_team_category == "default",
                        comp_qry.c.team_category != "default",
                    )
                )
            )
            .order_by(nullslast(desc(suit_score)))
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
        )
        proposed_ids = {
            row.request_id
            for row in db.query(CommunityProposal.request_id)
            .filter(CommunityProposal.player_id == player_id)
            .all()
        }

        community_rows = [
            u._asdict() for u in query.filter(comp_qry.c.is_community == True).all()
        ]
        # Exclude any row whose request_id is in proposed_ids
        community = [
            row for row in community_rows if row["request_id"] not in proposed_ids
        ]
        private = [
            u._asdict() for u in query.filter(comp_qry.c.is_community == False).all()
        ]
        return {"community": community, "private": private}

    # .distinct(comp_qry.c.teamId, comp_qry.c.position, comp_qry.c.transfer_period)

    def get_player_request_suitability(
        self, db: Session, org_id: Any, request_id: Any, player_id: Any
    ) -> Optional[ModelType]:
        team_qry = (
            db.query(
                TeamRequest.id,
                CompTeamElos.rating,
                CompTeamElos.name.label("league_name"),
                TeamRequest.position,
                TeamRequest.teamId,
                CompTeamElos.competitionId,
                TeamRequest.is_community,
                CompTeamElos.category.label("team_category"),
            )
            .join(CompTeamElos, TeamRequest.teamId == CompTeamElos.teamId, isouter=True)
            .filter((TeamRequest.id == request_id))
            .subquery()
        )

        # Top 5 players average value subquery - this is the crucial part!
        # Calculate average value of the 5 most expensive players for each team
        top5_avg_subq = (
            text(
                """
                SELECT
                    team_id,
                    AVG(current_value) as top5_avg_value
                FROM (
                    SELECT
                        "currentTeamId" as team_id,
                        current_value,
                        ROW_NUMBER() OVER (PARTITION BY "currentTeamId" ORDER BY current_value DESC) as rn
                    FROM wyscout.player_info2
                    WHERE current_value IS NOT NULL
                ) ranked
                WHERE rn <= 5
                GROUP BY team_id
                """
            )
            .columns(team_id=Integer, top5_avg_value=Float)
            .alias("top5_avg")
        )

        comp_qry = (
            db.query(
                CompElos.competitionId,
                CompElos.comp_median_rating,
                team_qry.c.position,
                team_qry.c.teamId,
                team_qry.c.rating,
                team_qry.c.is_community,
                team_qry.c.team_category,
            )
            .add_column(top5_avg_subq.c.top5_avg_value.label("top5_avg_value"))
            .join(
                team_qry,
                team_qry.c.competitionId == CompElos.competitionId,
                isouter=True,
            )
            .join(
                top5_avg_subq,
                top5_avg_subq.c.team_id == team_qry.c.teamId,
                isouter=True,
            )
            .subquery()
        )

        player_min_suit = (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        ) * (
            func.least(70.0, func.coalesce(PlayerYearMinutes.avg_mins_in_year, 0.0))
            / 70.0
        )
        comp_elo_suit = (
            1
            - func.least(
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating,
                1,
            )
        ) * (
            1
            - (
                func.abs(CompElos.comp_median_rating - comp_qry.c.comp_median_rating)
                / comp_qry.c.comp_median_rating
            )
        )
        team_elo_suit = (
            1
            - func.least(
                func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating, 1
            )
        ) * (
            1 - (func.abs(CompTeamElos.rating - comp_qry.c.rating) / comp_qry.c.rating)
        )

        # Financial suitability calculation using top 5 most valuable players
        financial_suit = case(
            (
                and_(
                    PlayerInfo.current_value.isnot(None),
                    PlayerInfo.current_value > 0,
                    comp_qry.c.top5_avg_value.isnot(None),
                    comp_qry.c.top5_avg_value > 0,
                ),
                func.least(1.0, comp_qry.c.top5_avg_value / PlayerInfo.current_value),
            ),
            else_=1.0,
        )

        # Updated suitability score with financial component (weights: 0.65, 0.25, 0.10)
        base_suit_score = (
            0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit
        ) * (0.65 * team_elo_suit + 0.25 * comp_elo_suit + 0.10 * player_min_suit)

        # Apply financial suitability factor
        suit_score = base_suit_score * (1 - 0.6 * (1 - financial_suit))
        suit_rating = case(
            (suit_score < 0.1, 0),
            ((suit_score >= 0.1) & (suit_score < 0.2), 1.0),
            ((suit_score >= 0.2) & (suit_score < 0.3), 1.5),
            ((suit_score >= 0.3) & (suit_score < 0.4), 2.0),
            ((suit_score >= 0.4) & (suit_score < 0.5), 2.5),
            ((suit_score >= 0.5) & (suit_score < 0.6), 3.0),
            ((suit_score >= 0.6) & (suit_score < 0.7), 3.5),
            ((suit_score >= 0.7) & (suit_score < 0.8), 4),
            ((suit_score >= 0.8) & (suit_score < 0.9), 4.5),
            ((suit_score >= 0.9) & (suit_score <= 1), 5),
            else_=None,
        )

        query = (
            db.query(
                PlayerInfo.team_name,
                self.model.playerId,
                self.model.id.label("player_id"),
                literal(request_id).label("request_id"),
                PlayerInfo.fullName,
                PlayerInfo.shortName,
                PlayerInfo.firstName,
                PlayerInfo.lastName,
                PlayerInfo.birthDate,
                PlayerInfo.birthArea_name.label("birth_area"),
                PlayerInfo.passport,
                PlayerInfo.eu,
                self.model.quality,
                self.model.potential,
                self.model.control_stage,
                self.model.transfer_strategy,
                PlayerInfo.foot,
                PlayerInfo.player_role,
                PlayerInfo.agent,
                PlayerInfo.player_url,
                self.model.last_updated,
                PlayerInfo.primary_ws_position,
                PlayerInfo.secondary_ws_position,
                PlayerInfo.birthDate,
                PlayerInfo.team_name,
                PlayerInfo.passport,
                PlayerInfo.tm_link,
                PlayerInfo.current_value.label("tm_value"),
                comp_qry.c.teamId.label("hiring_team_id"),
                team_qry.c.league_name,
                self.model.position.label("player_pos"),
                comp_qry.c.position.label("requested_pos"),
                self.model.club_asking_price,
                self.model.current_gross_salary,
                CompTeamElos.rating,
                comp_qry.c.rating.label("hiring_team_rating"),
                CompElos.comp_median_rating,
                comp_qry.c.comp_median_rating.label("hiring_comp_rating"),
                PlayerYearMinutes.avg_mins_in_year,
            )
            .add_column(team_elo_suit.label("team_elo_suitability"))
            .add_column(comp_elo_suit.label("comp_elo_suitability"))
            .add_column(player_min_suit.label("player_min_suitability"))
            .add_column(financial_suit.label("financial_suitability"))
            .add_column(comp_qry.c.top5_avg_value.label("top5_team_avg_value"))
            .add_column(suit_score.label("suitability_score"))
            .add_column(suit_rating.label("suitability_rating"))
            .join(PlayerInfo, self.model.playerId == PlayerInfo.playerId)
            .join(
                PlayerYearMinutes,
                self.model.playerId == PlayerYearMinutes.playerId,
                isouter=True,
            )
            .join(
                CompTeamElos,
                PlayerInfo.currentTeamId == CompTeamElos.teamId,
                isouter=True,
            )
            .join(
                CompElos,
                CompElos.competitionId == CompTeamElos.competitionId,
                isouter=True,
            )
            .join(comp_qry, literal(1) == 1)
            .filter(
                (
                    (self.model.organization_id == org_id)
                    | (comp_qry.c.is_community == True)
                )
                & (self.model.id == player_id)
                # Add the category filtering condition
                & ~(
                    and_(
                        CompTeamElos.category == "default",
                        comp_qry.c.team_category != "default",
                    )
                )
            )
            .order_by(nullslast(desc(suit_score)))
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .first()
        )
        return query._asdict()


suit_score_joined = CRUDSuitabilityJoined(models.PlayerRecord)
