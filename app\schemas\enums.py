from enum import Enum


class Position(str, Enum):
    ST = "st"
    LW = "lw"
    RW = "rw"
    CB = "cb"
    CMF = "cmf"
    DMC = "dmc"
    RWB = 'rwb'
    LWB = 'lwb'
    LCB = "lcb"
    RCB = "rcb"
    GK = "gk"
    LB = "lb"
    RB = "rb"
    SS = "ss"
    CAM = "cam"
    COACH = "coach"


class Foot(str, Enum):
    left = "left"
    right = "right"
    both = "both"

class RankSelectFilters(str, Enum):
    agency = "agency"
    roles = "roles"
    areas = "areas"


class FootPreference(str, Enum):
    left = "left"
    right = "right"
    both = "both"
    no_prefence = "no_preference"


class ContactType(str, Enum):
    internal = "internal"
    partner = "partner"
    external = "external"


class RequestStage(str, Enum):
    not_processed_yet = "not_processed_yet"
    request_intake = "request_intake"
    players_proposed_intermediary = "players_proposed_intermediary"
    players_proposed_club = "players_proposed_club"
    negotiations_with_club = "negotiations_with_club"
    closed_won = "closed_won"
    closed_lost = "closed_lost"
    discarded_before_intake = "discarded_before_intake"
    unknown = "unknown"


class Segment(Enum):
    youth = 13
    A_plus = 12
    A = 11
    A_minus = 10
    B_plus = 9
    B = 8
    B_minus = 7
    C_plus = 6
    C = 5
    C_minus = 4
    D_plus = 3
    D = 2
    D_minus = 1
    pending_review = -1


class Type(Enum):
    free_transfer = "free_transfer"
    loan_with_option = "loan_with_option"
    loan = "loan"
    transfer = "transfer"

class ConstrolStage(str, Enum):
    watchlist = "watchlist"
    target = "target"
    in_talks = "in_talks"
    singed = "signed"
    on_hold = "on_hold"
    closed = "closed"
    mandate = "mandate"
    mandate_on_demand = "mandate_on_demand"


class ReportType(str, Enum):
    live = "live"
    video = "video"
    data = "data"
    personal = "personal"


class TransferPeriod(str, Enum):
    winter_2020 = "winter_2020"
    summer_2020 = "summer_2020"
    winter_2021 = "winter_2021"
    summer_2021 = "summer_2021"
    winter_2022 = "winter_2022"
    summer_2022 = "summer_2022"
    winter_2023 = "winter_2023"
    summer_2023 = "summer_2023"
    winter_2024 = "winter_2024"
    summer_2024 = "summer_2024"
    winter_2025 = "winter_2025"
    summer_2025 = "summer_2025"
    winter_2026 = "winter_2026"
    summer_2026 = "summer_2026"
    winter_2027 = "winter_2027"
    summer_2027 = "summer_2027"

class ControlLevel(str, Enum):
    i_am_not_the_agent = "i_am_not_the_agent"
    i_have_mandate = "i_have_mandate"
    i_am_the_agent = "i_am_the_agent"


class ContractType(str, Enum):
    mandate = "mandate"
    representation_agreement = "representation_agreement"
    club_contract = "club_contract"
    commission_agreement = "commission_agreement"
    commercial = "commercial"

class Status(str, Enum):
    open = "open"
    closed = "closed"
    maybe = "maybe"

class ChannelType(str, Enum):
    social_media = "social_media"
    runner = "runner"
    direct = "direct"
    referral = "referral"
    other = "other"

class Features(str, Enum):
    good = "good"
    medium = "medium"
    poor = "poor"

class ActivityStage(str, Enum):
    to_do = "to_do"
    in_progress = "in_progress"
    done = "done"
    offered = "offered"
    interest = "interest"
    no_interest = "no_interest"
    negotiations = "negotiations"

class ActivityType(str, Enum):
    task = "task"
    deal = "deal"

class CommunityDealType(str, Enum):
    proposed = "proposed"
    received = "received"

class Feedback(str, Enum):
    i_am_interested = "i_am_interested"
    financials = "financials"
    quality = "quality"
    playing_style = "playing_style"  #No interest in front

class TransferStrategy(str, Enum):
    sell = "sell"
    load = "loan"
    retain = "retain"

class TransferStrategyStaff(str, Enum):
    open = "open"
    selective = "selective"
    unavailable = "unavailable"

class InAppNotificationType(str, Enum):
    team_requests_create = "team_requests_create"
    player_records_create = "player_records_create"
    activity_create = "activity_create"
    activity_update = "activity_update"
    community_proposals = "community_proposals"
    community_proposal_feedback = "community_proposal_feedback"

class StaffRoles(str, Enum):
    head_coach = "head_coach"
    assistant_coach = "assistant_coach"
    coaching_staff = "coaching_staff"
    sporting_director = "sporting_director"
    medical_staff = "medical_staff"
    scouting = "scouting"
    club_executives = "club_executives"
    other_staff = "other_staff"
    analyst = "analyst"
    administrative_staff = "administrative_staff"
    academy_staff = "academy_staff"


class TagType(str, Enum):
    players = "players"
    teams = "teams"