from sqlalchemy import Column, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID
import uuid


class PriorityPlayers(Base):
    __tablename__ = "priority_players"
    __table_args__ = (
        UniqueConstraint(
            "user_id", "player_record_id", name="_user_player_priority_uc"
        ),
        {"schema": settings.PG_SCHEMA},
    )

    id = Column(UUID(as_uuid=True), primary_key=True, index=True, default=uuid.uuid4)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.user.id"),
        index=True,
        nullable=False,
    )
    player_record_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.player_records.id"),
        index=True,
        nullable=False,
    )

    # Relationships
    user = relationship("User")
    player_record = relationship("PlayerRecord")
