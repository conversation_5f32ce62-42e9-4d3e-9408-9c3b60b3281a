from sqlalchemy import Column, String, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID
import uuid


class FavouriteTeams(Base):
    __tablename__ = "favourite_teams"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(UUID(as_uuid=True), primary_key=True, index=True, default=uuid.uuid4)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.user.id"),
        index=True,
    )
    teamId = Column(Integer, ForeignKey("wyscout.team_info2.teamId"), index=True)

    # Relationships
    user = relationship("User")
    team_info = relationship(
        "TeamInfo",
        viewonly=True,
        primaryjoin="foreign(FavouriteTeams.teamId)==remote(TeamInfo.teamId)",
    )
