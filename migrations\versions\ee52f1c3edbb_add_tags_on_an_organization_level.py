"""Add tags on an organization level

Revision ID: ee52f1c3edbb
Revises: 8c9f417a57fe
Create Date: 2025-08-13 11:43:28.968616

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ee52f1c3edbb'
down_revision = '8c9f417a57fe'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organization_tags',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('type_of_tags', sa.String(), nullable=False),
    sa.Column('tags', postgresql.ARRAY(sa.String()), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_organization_tags_organization_id'), 'organization_tags', ['organization_id'], unique=False, schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_crm_test_organization_tags_organization_id'), table_name='organization_tags', schema='crm_test')
    op.drop_table('organization_tags', schema='crm_test')
    # ### end Alembic commands ###