# Import all the models, so that Base has them before being
# imported by <PERSON><PERSON><PERSON>
from app.db.base_class import Base
from app.models.user import User
from app.models.user_role import UserRole
from app.models.contact import Contact
from app.models.organization import Organization
from app.models.player_record import PlayerRecord
from app.models.player_features import PlayerFeatures
from app.models.team_request import TeamRequest
from app.models.report import Report
from app.models.purchase import Purchase
from app.models.proposal import Proposal
from app.models.module import Module
from app.models.player_record_change import PlayerRecordChange
from app.models.team_request_change import TeamRequestChange
from app.models.activity_change import ActivityChange

from app.models.player_info import PlayerInfo
from app.models.player_aggregate_stats import PlayerAggregateStats
from app.models.team_info import TeamInfo
from app.models.rank_output import RankOutput
from app.models.rank_record import RankRecord
from app.models.ranking_cutoff_filters import CutoffFilters
from app.models.ranking_position_weights import DefaultWeights
from app.models.ranking_variable_definitions import VariableDefinition
from app.models.ranking_comparable_positions import ComparablePositions
from app.models.contract import Contract, Mandate, RepresentationAgreement, ClubContract, CommissionAgreement, ContractUpload
from app.models.source_to_record import SourceToRecord
from app.models.organization_tag import OrganizationTag