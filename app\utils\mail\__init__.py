from typing import List
from app import models
from app.models import (
    TeamRequest,
    Activity,
    Contact,
    PlayerRecord,
    StaffRecord
)
from app.config import settings
from .Mailer import Mailer, ContentType, ReportContent
from app.utils import snake_to_title, suitability_score_to_text, send_token_push
from sqlalchemy.orm import class_mapper
# from app.api.endpoints.contacts import read_contacts, read_contact
from sqlalchemy.orm import Session
from app.api.endpoints.users.db import User
import app.api.endpoints.users as users
import requests
import jinja2
import os
import math
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Personalization, Bcc, To
import asyncio
from app.schemas.platform_notifications import PlatformNotificationCreate
from app.utils import get_url_frontend

IMAGE_PATHS = [
    "enskai_logo_1.png",
    "facebook-rounded-colored-bordered.png",
    "instagram-rounded-colored-bordered.png",
    "rectangle_243_1.png",
    "rectangle_243.png",
    "twitter-rounded-colored-bordered.png",
]


async def get_tokens(emails, db, func_sub):
    # Run read_subscription_func concurrently for all emails
    subscription_lists = await asyncio.gather(
        *(func_sub(email, db=db) for email in emails)
    )
    # Flatten the list of lists
    subscriptions = [
        subscription for sublist in subscription_lists for subscription in sublist
    ]
    # Extract the tokens
    tokens = [subscription.token for subscription in subscriptions]
    return tokens


def format_number(num) -> str:
    if num == "Not specified" or num == "Upon request":
        return num
    if not num:
        return "0"

    try:
        if isinstance(num, str):
            num = num.replace(",", "")
        num = float(num)
    except ValueError:
        return "0"

    if abs(num) < 1000:
        return str(num)

    units = ["k", "m", "b"]
    unit_index = math.floor(math.log10(abs(num)) / 3)
    divisor = 1000**unit_index
    formatted_num: str

    if unit_index == 1:  # 'k'
        formatted_num = f"{num / divisor:.0f}"
    elif unit_index == 2:  # 'm'
        formatted_num = f"{num / divisor:.1f}"
    else:  # 'b' and others
        formatted_num = f"{num / divisor:.2f}"

    return f"{formatted_num}{units[unit_index - 1]}"


def make_core_info(req: TeamRequest, pos_list: List[str]) -> str:
    value = (
        format_number(req.max_value) if req.max_value is not None else "Not specified"
    )
    return f"""
            <h3>Positions: {', '.join(pos_list)}</h3>
            <p>Details: {req.description}</p>
            <p>Type: {', '.join([snake_to_title(x) for x in req.type])}</p>
            <p>Max transfer fee (EUR): {value}</p>
            """


async def get_matching_names(
    req: TeamRequest,
) -> str:
    resp = requests.get(
        f"{settings.MATCHING_API_URL}/match/request/{req.id}",
        auth=(settings.MATCHING_API_USR, settings.MATCHING_API_PASS),
    )
    names = " ".join(
        f"<li>{x['player_info']['firstName']} {x['player_info']['lastName']} from"
        f" {x['player_info']['team_name']}</li>"
        for x in resp.json()
    )
    return (
        f"""<h2>Currently matching players for {req.position.upper()}:</h2>
                <ul>{names}</ul>"""
        if names
        else "<h2>No matching players at the moment</h2>"
    )

def sqlalchemy_to_dict(obj):
    """Convert a SQLAlchemy object into a dictionary."""
    return {col.key: getattr(obj, col.key) for col in class_mapper(obj.__class__).columns}

def process_data(data):
    # Fields to process
    fields_to_group = ["max_value", "foot", "max_net_salary", "description", "eu_passport"]
    result = {}

    # Iterate over fields
    for field in fields_to_group:
        unique_values = {}
        for item in data:
            item = sqlalchemy_to_dict(item)
            position = item.get("position", "Unknown").upper()  # Capitalize the position
            value = item.get(field, "N/A")  # Default to "N/A" if value is missing or None
            
            # Handle empty strings or None explicitly
            if value in [None, "", []]:
                value = "N/A"
            
            # Apply format_number to numeric fields
            if field in ["max_value", "max_net_salary"] and isinstance(value, (int, float)):
                value = format_number(value)
            
            if field == 'foot':
                value = snake_to_title(value)
            
            # Format boolean values
            if isinstance(value, bool):
                value = "Yes" if value else "No"
            
            # Store values grouped by position
            unique_values.setdefault(value, []).append(position)
        
        # Format the result for the current field
        if len(unique_values) == 1:
            # If all values are the same, just store the single value
            single_value = next(iter(unique_values))
            result[field] = str(single_value)
        else:
            # Special handling for `description`: Join with a newline
            if field == "description":
                grouped = "\n".join(
                    f"{str(value)} ({', '.join(pos_list)})"
                    for value, pos_list in unique_values.items()
                )
            else:
                # Group other fields with commas
                grouped = ", ".join(
                    f"{str(value)} ({', '.join(pos_list)})"
                    for value, pos_list in unique_values.items()
                )
            result[field] = grouped
    
    return result



async def generate_request_mail_content(team_request_list: List[TeamRequest]):
    team_request = team_request_list[0]
    pos_list = ", ".join([x.position.upper() for x in team_request_list])

    source_str = team_request.creator.email
    if team_request.source_to_record is not None:
        source_str = ", ".join(
            [
                f"{x.source.first_name} {x.source.last_name}"
                for x in team_request.source_to_record
            ]
        )
    req_type = ", ".join([snake_to_title(x) for x in team_request.type])

    result = process_data(team_request_list)

    data_file_path = os.path.join(os.path.dirname(__file__), "request.html")
    with open(data_file_path) as file_:
        template = jinja2.Template(file_.read())

    data = {
        "request": team_request,
        "pos_list": pos_list,
        "source_str": source_str,
        "req_type": req_type,
        "max_transfer_value": result['max_value'],
        "max_net_salary": result['max_net_salary'],
        "eu_passport": result['eu_passport'],
        "foot": result['foot'],
        "description": result['description'],
    }

    partner_body = template.render(data)
    return partner_body


def get_emails(contacts: List[Contact]):
    if not settings.PROD:
        return [
            "<EMAIL>",
        ]  # ["<EMAIL>"] #,
    # internal_contacts = [
    #    x.email for x in contacts if x.contact_type == "internal"
    # ]
    partner_contacts = [
        x.email for x in contacts if x.contact_type in ("partner", "internal")
    ]
    return partner_contacts


def generate_mail_config(partner_contacts, external_body: str):
    mail_config = {
        "external": {
            "recipients": partner_contacts,
            "content": [],
        },
    }

    mail_config["external"]["content"].append(
        ReportContent(
            type=ContentType.body_html,
            content=external_body,
        )
    )

    return mail_config


def make_subject(team_request_list: List[TeamRequest]) -> str:
    tr = team_request_list[0]
    return (
        f"New Request | {tr.team_info.area_name} | {tr.team_info.name} "
        f"| {', ' .join([x.position.upper() for x in team_request_list])} "
        f"| {', '.join([snake_to_title(x) for x in tr.transfer_period])}"
    )


async def send_mail_new_request(
    team_request_list: List[TeamRequest],
    current_user: models.User,
    db: Session,
    read_contacts_func,
    read_subscription_func,
    emails,
    create_in_app_notifications_bulk,
):
    """
    Make a new request email.
    """
    if (read_contacts_func(current_user=current_user, db=db)) is None:
        return

    partner_contacts = emails
    partner_body = await generate_request_mail_content(team_request_list)

    message = Mail(
        from_email="<EMAIL>",
        subject=make_subject(team_request_list),
        html_content=partner_body,
    )

    personalization = Personalization()
    personalization.add_to(To("<EMAIL>"))

    for email in partner_contacts:
        if email:
            personalization.add_bcc(Bcc(email))

    message.add_personalization(personalization)

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        sg.send(message)
    except Exception as e:
        print(e.message)

    user_ids = [
        user_out["id"]
        for user_out in await asyncio.gather(
            *[
                users.users.get_user_by_email_homemade(
                    email=email, db=db, current_active_user=current_user
                )
                for email in emails
            ]
        )
        if user_out
    ]
    notifications = [
        PlatformNotificationCreate(
            active=True,
            type="team_requests_create",
            team_request_id=team_request.id,
            created_for="fcb0f612-3a9c-498d-bc25-f10b79054e09",  # Elvan id
            created_by=current_user.id,
            organization_id=current_user.organization_id,
        )
        for team_request in team_request_list
    ]
    await create_in_app_notifications_bulk(
        db=db,
        current_user=current_user,
        platform_notifications_in=notifications,
        user_ids=user_ids,
    )

    devices = await get_tokens(emails, db, read_subscription_func)
    source_str = team_request_list[0].creator.email
    if team_request_list[0].source_to_record is not None:
        source_str = ", ".join(
            [
                f"{x.source.first_name} {x.source.last_name}"
                for x in team_request_list[0].source_to_record
            ]
        )
    send_token_push(
        "",
        f"{team_request_list[0].team_info.name} ({team_request_list[0].team_info.area_name}) is looking for {', '.join([x.position.upper() for x in team_request_list])}. By: {source_str}",
        devices,
        f"/team_requests?team_id={team_request_list[0].id}"
    )


async def send_mail_new_player(
    player: PlayerRecord,
    current_user: models.User,
    db: Session,
    read_contact_func,
    read_subscription_func,
    create_in_app_notification,
    assigned_id=None,
):

    if assigned_id is None:
        return
    assigned_info = read_contact_func(id=assigned_id, current_user=current_user, db=db)
    if (
        assigned_info is None
        or assigned_info.email is None
        or assigned_info.contact_type != "internal"
    ):
        return
    subject = (
        "New assigned player:"
        f" {player.player_info.firstName} {player.player_info.lastName}"
    )

    data_file_path = os.path.join(os.path.dirname(__file__), "assigned_player.html")
    with open(data_file_path) as file_:
        template = jinja2.Template(file_.read())

    if player.description is None:
        player.description = "No additional info"

    old_control = player.control_stage
    if player.control_stage is not None:
        player.control_stage = snake_to_title(player.control_stage)
    pos_list = ", ".join([x.upper() for x in player.position])
    data = {
        "player": player,
        "pos_list": pos_list,
        "creator": current_user.email,
    }

    body = template.render(data)

    message = Mail(
        from_email="<EMAIL>",
        subject=subject,
        to_emails=[assigned_info.email],
        html_content=body,
    )

    #try:
    #    sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
    #    sg.send(message)
    #except Exception as e:
    #    print(e.message)
    player.control_stage = old_control
    notification = PlatformNotificationCreate(
        active=True,
        type="player_records_create",
        player_record_id=player.id,
        created_for=assigned_id,
    )
    await create_in_app_notification(
        db=db, current_user=current_user, platform_notification_in=notification
    )
    devices = [el.token for el in await read_subscription_func(assigned_id, db=db)]
    send_token_push(
        "New assigned player",
        f"{player.player_info.firstName} {player.player_info.lastName} ({pos_list}) is assigned to you by {current_user.email}",
        devices,
        f"/portfolio/players?player_id={player.id}"
    )

async def send_mail_new_staff(
    staff: StaffRecord,
    current_user: models.User,
    db: Session,
    read_contact_func,
    read_subscription_func,
    assigned_id=None,
):

    if assigned_id is None:
        return
    assigned_info = read_contact_func(id=assigned_id, current_user=current_user, db=db)
    if (
        assigned_info is None
        or assigned_info.email is None
        or assigned_info.contact_type != "internal"
    ):
        return
    subject = (
        "New assigned staff:"
        f" {staff.staff_info.name}"
    )

    data_file_path = os.path.join(os.path.dirname(__file__), "assigned_staff.html")
    with open(data_file_path) as file_:
        template = jinja2.Template(file_.read())

    if staff.description is None:
        staff.description = "No additional info"

    pos_list = ", ".join([x.upper() for x in staff.roles])
    data = {
        "staff": staff,
        "pos_list": pos_list,
        "creator": current_user.email,
        "control_stages": snake_to_title(staff.control_stage)
    }

    body = template.render(data)

    message = Mail(
        from_email="<EMAIL>",
        subject=subject,
        to_emails=[assigned_info.email],
        html_content=body,
    )

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        sg.send(message)
    except Exception as e:
        print(e.message)
    devices = [el.token for el in await read_subscription_func(assigned_id, db=db)]
    send_token_push(
        "New assigned staff",
        f"{staff.staff_info.name} ({pos_list}) is assigned to you by {current_user.email}",
        devices,
        f"/portfolio/staff?staff_id={staff.id}"
    )


async def send_mail_new_activity(
    activity: Activity,
    current_user: models.User,
    db: Session,
    read_contact_func,
    read_subscription_func,
    create_in_app_notification,
    assigned_id,
    mail_type="create",
    email=None,
    user_id=None,
):
    emails = []
    #subject = "New assigned activity"
    #title = "A new activity has been assigned to you by:"
    title_push = "Assigned by"
    updator = ''
    if email:
        updator = email
    else:
        updator = current_user.email
    if user_id:
        my_id = user_id
    else:
        my_id = current_user.id
    if mail_type == "update":
        #subject = f"Updated activity by {activity.updated_by.first_name} {activity.updated_by.last_name}"
        #title = "An activity has been updated by:"
        title_push = "By"
        updator = f"{activity.updated_by.first_name} {activity.updated_by.last_name}"

    if mail_type == "create":
        if assigned_id is None:
            return
    if assigned_id != my_id and assigned_id is not None:
        assigned_info = read_contact_func(
            id=assigned_id, current_user=current_user, db=db
        )
        if (
            assigned_info is not None
            and assigned_info.email is not None
            and assigned_info.contact_type == "internal"
        ):
            emails.append(assigned_info.email)
            assigned_id = assigned_info.id
    if mail_type == "update" and activity.created_by != my_id:
        if activity.creator.email not in emails:
            emails.append(activity.creator.email)

    if not emails:
        return

    #data_file_path = os.path.join(os.path.dirname(__file__), "activity.html")
    #with open(data_file_path) as file_:
    #    template = jinja2.Template(file_.read())

    if activity.description is None:
        activity.description = "No additional info"
    #data = {
    #    "type": activity.type.capitalize(),
    #    "activity": activity,
    #    "title": title,
    #    "updator": updator,
    #    "stage": snake_to_title(activity.stage) if activity.stage else activity.stage,
    #    "next_action": (
    #        "N/A"
    #        if activity.next_action is None
    #        else activity.next_action.strftime("%Y-%m-%d")
    #    ),
    #    "staff": "N/A" if activity.staff is None else activity.staff,
    #    "description": (
    #        "No additional info"
    #        if activity.description is None
    #        else activity.description
    #    ),
    #}
    #body = template.render(data)

    #message = Mail(
    #    from_email="<EMAIL>", subject=subject, html_content=body
    #)

    #personalization = Personalization()
    #personalization.add_to(To("<EMAIL>"))

    #for email in emails:
    #    personalization.add_bcc(Bcc(email))

    #message.add_personalization(personalization)

    #try:
    #    sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
    #    sg.send(message)
    #except Exception as e:
    #    print(e.message)

    if assigned_id:
        notification = PlatformNotificationCreate(
            active=True,
            type=f"activity_{mail_type}",
            activity_id=activity.id,
            created_for=assigned_id,
        )
        await create_in_app_notification(
            db=db, current_user=current_user, platform_notification_in=notification
        )

    devices = await get_tokens(emails, db, read_subscription_func)
    new_activity = ""
    if activity.next_action:
        new_activity = activity.next_action.strftime("%Y-%m-%d")

    send_token_push(
        "",
        f"{activity.type.capitalize()}: {activity.title}. {'Next action' if activity.type == 'deal' else 'Due'}: {new_activity}. {title_push}: {updator}",
        devices,
        f"/activity_tracker/all?activity_id={activity.id}"
    )


async def send_reset_password_link(user: User, token: str):
    url = get_url_frontend()

    content = ReportContent(
        type=ContentType.body_html,
        content=f"""<h2>Here is your link for password reset:</h2>
    <a href="{url}/reset_password/{token}">Reset Password</a>
    <p>Follow the link and enter your new password.</p>
    """,
    )

    mailer = Mailer(
        subject="Password reset link",
        recipients=[user.email],
        contents_list=[content],
    )
    mailer.send_mail()

async def send_welcome_email(url, email):
        subject = "Welcome to the EnskAI Platform"
        data_file_path = os.path.join(os.path.dirname(__file__), "welcome_email.html")
        with open(data_file_path) as file_:
            template = jinja2.Template(file_.read())
        data = {
            "email": email,
            "link": url,
        }
        body = template.render(data)
        message = Mail(
            from_email="<EMAIL>",
            subject=subject,
            to_emails=[email, '<EMAIL>'],
            html_content=body,
        )

        try:
            sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
            sg.send(message)
        except Exception as e:
            print(e)


async def send_community_deal_feedback(
    community_deal, opposite_community_id, opposite_community_creator_id,
    create_in_app_notification,
    db:Session, 
    current_user,
    read_subscription_func,
):
    subject = "New feedback on proposed player"
    data_file_path = os.path.join(os.path.dirname(__file__), "feedback.html")
    with open(data_file_path) as file_:
        template = jinja2.Template(file_.read())
    data = {
        "community_deal": community_deal,
        "proposal": community_deal['community_proposal'],
    }
    body = template.render(data)
    to_email = community_deal['community_proposal']['creator']['email']
    message = Mail(
        from_email="<EMAIL>",
        subject=subject,
        to_emails=[to_email, "<EMAIL>"],
        html_content=body,
    )

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        sg.send(message)
    except Exception as e:
        print(e.message)

    notification = PlatformNotificationCreate(
        active=True,
        type="community_proposal_feedback",
        community_deal_id=opposite_community_id,
        created_for=opposite_community_creator_id,
    )

    await create_in_app_notification(
        db=db,
        current_user=current_user,
        platform_notification_in=notification,
    )
    devices = await get_tokens([to_email], db, read_subscription_func)
    send_token_push(
        "New feedback received",
        f"""Agent: {community_deal['creator']['email']} is interested in your player - {community_deal.community_proposal.player_records.player_info.shortName}""",
        devices,
        f"/activity_tracker/all?community_deal_id={opposite_community_id}"
    )


async def send_support_del_acc_mail(user: User):
    content = ReportContent(
        type=ContentType.body_html,
        content=f"""<h2>User {user.email} wants his account deleted</h2>
    <p>User id: {user.id}</p>
    <p>User organization_id: {user.organization_id}</p>
    """,
    )

    mailer = Mailer(
        subject="Delete account",
        recipients=["<EMAIL>"],
        contents_list=[content],
    )
    mailer.send_mail()


async def send_verification_link(user: User, token: str):
    url = get_url_frontend()

    data_file_path = os.path.join(os.path.dirname(__file__), "verification.html")

    with open(data_file_path) as file_:
        template = jinja2.Template(file_.read())

    data = {"verification": f"{url}/verify/{token}"}
    outputText = template.render(data)

    message = Mail(
        from_email="<EMAIL>",
        to_emails=user.email,
        subject="Email verification",
        html_content=outputText,
    )

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        sg.send(message)
        print(f"Send verification email for {user.email}")
    except Exception as e:
        print(e.message)


async def send_mail_new_proposal(
    request: dict,
    receiver_email: str,
    player: dict,
    community_proposal: dict,
    community_deal_id: int,
    current_user: models.User,
    mins_played: int,
    suitability_score: str,
    read_subscription_func,
    db,
    create_in_app_notifications_bulk,
):
    sender_email = current_user.email

    # Modifying the data which is passed to the html:
    if community_proposal['are_you_the_agent'] == "i_am_the_agent":
        agency_text = "is the agent"
    elif community_proposal['are_you_the_agent'] == "i_have_mandate":
        agency_text = "has a mandate"
    else:
        agency_text = "is not the agent"

    subject = f"New proposed player to your request for: {request['team_info'].name}"

    passport = [player['player_info'].passport]
    if player['player_info'].passport != player['player_info'].birth_area:
        passport.append(player['player_info'].birth_area)
    passport = ", ".join(passport)

    if community_proposal['description'] is None:
        community_proposal['description'] = "No additional info"

    old_community_salary = community_proposal['expected_salary']
    if not community_proposal['expected_salary']:
        community_proposal['expected_salary'] = "Upon request"

    suitability_text = suitability_score_to_text[suitability_score]

    data_file_path = os.path.join(os.path.dirname(__file__), "community.html")

    community_proposal['expected_salary'] = format_number(
        community_proposal['expected_salary']
    )
    old_asking_price = community_proposal['club_asking_price']
    community_proposal['club_asking_price'] = format_number(
        community_proposal['club_asking_price']
    )

    for mail in receiver_email:
        with open(data_file_path) as file_:
            template = jinja2.Template(file_.read())
        data = {
            "request": request,
            "player": player,
            "community_proposal": community_proposal,
            "agency_text": agency_text,
            "receiver_email": mail,
            "sender_email": sender_email,
            "passport": passport,
            "num_received": len(receiver_email),
            "mins_played": mins_played,
            "suitability_score": suitability_score,
            "suitability_text": suitability_text,
        }
        outputText = template.render(data)

        message = Mail(
            from_email="<EMAIL>",
            subject=subject,
            html_content=outputText,
            to_emails=[mail, "<EMAIL>"],
        )

        try:
            sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
            sg.send(message)

        except Exception as e:
            print("Error: unable to send email")
            raise e

        community_proposal['expected_salary'] = old_community_salary
        community_proposal['club_asking_price'] = old_asking_price

        user_ids = [
            user_out["id"]
            for user_out in await asyncio.gather(
                *[
                    users.users.get_user_by_email_homemade(
                        email=email, db=db, current_active_user=current_user
                    )
                    for email in receiver_email
                ]
            )
            if user_out
        ]

        notifications = [
            PlatformNotificationCreate(
                active=True,
                type="community_proposals",
                community_deal_id=community_deal_id,
                created_for="fcb0f612-3a9c-498d-bc25-f10b79054e09",  # Elvan id
                created_by=current_user.id,
                organization_id=current_user.organization_id,
            )
        ]

        await create_in_app_notifications_bulk(
            db=db,
            current_user=current_user,
            platform_notifications_in=notifications,
            user_ids=user_ids,
        )

        devices = [
            el.token
            for el in await read_subscription_func(user_id=request['created_by'], db=db)
        ]
        send_token_push(
            f"",
            f"""Community: {sender_email} proposed {player['player_info'].firstName} {player['player_info'].lastName} for {request['team_info'].name} ({request['position'].upper()}). Check your email for full details.""",
            devices,
            f"/activity_tracker/all?community_deal_id={community_deal_id}"
        )

async def send_support_mail(
    player,
    current_user: models.User,
):
    data_file_path = os.path.join(os.path.dirname(__file__), "support_manual_player.html")
    with open(data_file_path) as file_:
        template = jinja2.Template(file_.read())
    data = {
        "player": player,
        "from": current_user.email,
    }
    outputText = template.render(data)

    message = Mail(
        from_email="<EMAIL>",
        subject="Manually create player - request",
        html_content=outputText,
        to_emails=["<EMAIL>", "<EMAIL>"],
    )

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        sg.send(message)

    except Exception as e:
        print("Error: unable to send email")
        raise e